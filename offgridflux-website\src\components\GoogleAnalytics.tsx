// OffGridFlux - Google Analytics Component
// Client-side analytics initialization and tracking

'use client';

import { useEffect } from 'react';
import { usePathname } from 'next/navigation';
import {
  initGA,
  trackPageView,
  initScrollTracking,
  initTimeTracking,
  GA_MEASUREMENT_ID
} from '@/lib/analytics';

export function GoogleAnalytics() {
  const pathname = usePathname();

  useEffect(() => {
    // Initialize Google Analytics
    initGA();

    // Initialize scroll and time tracking
    const cleanupScroll = initScrollTracking();
    const cleanupTime = initTimeTracking();

    return () => {
      if (cleanupScroll) cleanupScroll();
      if (cleanupTime) cleanupTime();
    };
  }, []);

  useEffect(() => {
    // Track page views on route changes
    trackPageView(pathname);
  }, [pathname]);

  // Only render in production with valid measurement ID
  if (process.env.NODE_ENV !== 'production' || !GA_MEASUREMENT_ID || GA_MEASUREMENT_ID === 'G-XXXXXXXXXX') {
    return null;
  }

  return (
    <>
      {/* Google Analytics Script */}
      <script
        async
        src={`https://www.googletagmanager.com/gtag/js?id=${GA_MEASUREMENT_ID}`}
      />
      <script
        dangerouslySetInnerHTML={{
          __html: `
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', '${GA_MEASUREMENT_ID}', {
              page_title: document.title,
              page_location: window.location.href,
            });
          `,
        }}
      />
    </>
  );
}

// Hook for tracking affiliate clicks
export function useAffiliateTracking() {
  const trackClick = (
    productName: string,
    productId: string,
    brand: string,
    price: number,
    url: string
  ) => {
    // Import analytics functions dynamically to avoid SSR issues
    import('@/lib/analytics').then(({ trackAffiliateClick }) => {
      trackAffiliateClick(productName, productId, brand, price, url);
    });
  };

  return { trackClick };
}

// Hook for tracking product views
export function useProductTracking() {
  const trackView = (
    productName: string,
    productId: string,
    brand: string,
    price: number,
    category: string
  ) => {
    import('@/lib/analytics').then(({ trackProductView }) => {
      trackProductView(productName, productId, brand, price, category);
    });
  };

  return { trackView };
}

// Hook for tracking newsletter signups
export function useNewsletterTracking() {
  const trackSignup = (source: string) => {
    import('@/lib/analytics').then(({ trackNewsletterSignup }) => {
      trackNewsletterSignup(source);
    });
  };

  return { trackSignup };
}

// Hook for tracking search
export function useSearchTracking() {
  const trackSearch = (searchTerm: string, resultsCount: number) => {
    import('@/lib/analytics').then(({ trackSearch }) => {
      trackSearch(searchTerm, resultsCount);
    });
  };

  return { trackSearch };
}

// Hook for tracking content engagement
export function useContentTracking() {
  const trackEngagement = (
    contentType: string,
    contentId: string,
    engagementType: string,
    value?: number
  ) => {
    import('@/lib/analytics').then(({ trackContentEngagement }) => {
      trackContentEngagement(contentType, contentId, engagementType, value);
    });
  };

  return { trackEngagement };
}
