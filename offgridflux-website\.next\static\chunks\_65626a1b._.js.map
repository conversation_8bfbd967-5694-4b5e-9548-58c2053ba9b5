{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/offgridflux/offgridflux-website/src/lib/analytics.ts"], "sourcesContent": ["// OffGridFlux - Analytics Integration\n// Google Analytics 4 and conversion tracking\n\ndeclare global {\n  interface Window {\n    gtag: (...args: unknown[]) => void;\n    dataLayer: unknown[];\n  }\n}\n\n// Google Analytics 4 Configuration\nexport const GA_MEASUREMENT_ID = process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID || 'G-XXXXXXXXXX';\n\n// Initialize Google Analytics\nexport const initGA = () => {\n  if (typeof window === 'undefined') return;\n  \n  // Load Google Analytics script\n  const script = document.createElement('script');\n  script.src = `https://www.googletagmanager.com/gtag/js?id=${GA_MEASUREMENT_ID}`;\n  script.async = true;\n  document.head.appendChild(script);\n  \n  // Initialize dataLayer and gtag\n  window.dataLayer = window.dataLayer || [];\n  window.gtag = function gtag(...args: unknown[]) {\n    window.dataLayer.push(args);\n  };\n  \n  window.gtag('js', new Date());\n  window.gtag('config', GA_MEASUREMENT_ID, {\n    page_title: document.title,\n    page_location: window.location.href,\n  });\n};\n\n// Track page views\nexport const trackPageView = (url: string, title?: string) => {\n  if (typeof window === 'undefined' || !window.gtag) return;\n  \n  window.gtag('config', GA_MEASUREMENT_ID, {\n    page_title: title || document.title,\n    page_location: url,\n  });\n};\n\n// Track custom events\nexport const trackEvent = (\n  action: string,\n  category: string,\n  label?: string,\n  value?: number,\n  customParameters?: Record<string, unknown>\n) => {\n  if (typeof window === 'undefined' || !window.gtag) return;\n  \n  window.gtag('event', action, {\n    event_category: category,\n    event_label: label,\n    value: value,\n    ...customParameters,\n  });\n};\n\n// E-commerce and affiliate tracking\nexport const trackAffiliateClick = (\n  productName: string,\n  productId: string,\n  brand: string,\n  price: number,\n  url: string\n) => {\n  trackEvent('affiliate_click', 'ecommerce', productName, price, {\n    product_id: productId,\n    product_name: productName,\n    product_brand: brand,\n    product_price: price,\n    affiliate_url: url,\n    currency: 'USD',\n  });\n};\n\n// Track product views\nexport const trackProductView = (\n  productName: string,\n  productId: string,\n  brand: string,\n  price: number,\n  category: string\n) => {\n  if (typeof window === 'undefined' || !window.gtag) return;\n  \n  window.gtag('event', 'view_item', {\n    currency: 'USD',\n    value: price,\n    items: [\n      {\n        item_id: productId,\n        item_name: productName,\n        item_brand: brand,\n        item_category: category,\n        price: price,\n        quantity: 1,\n      },\n    ],\n  });\n};\n\n// Track newsletter signups\nexport const trackNewsletterSignup = (source: string) => {\n  trackEvent('newsletter_signup', 'engagement', source, undefined, {\n    signup_source: source,\n  });\n};\n\n// Track search queries\nexport const trackSearch = (searchTerm: string, resultsCount: number) => {\n  trackEvent('search', 'engagement', searchTerm, resultsCount, {\n    search_term: searchTerm,\n    results_count: resultsCount,\n  });\n};\n\n// Track content engagement\nexport const trackContentEngagement = (\n  contentType: string,\n  contentId: string,\n  engagementType: string,\n  value?: number\n) => {\n  trackEvent(engagementType, 'content', contentId, value, {\n    content_type: contentType,\n    content_id: contentId,\n  });\n};\n\n// Track scroll depth\nexport const trackScrollDepth = (percentage: number, page: string) => {\n  trackEvent('scroll_depth', 'engagement', page, percentage, {\n    scroll_percentage: percentage,\n    page_path: page,\n  });\n};\n\n// Track time on page\nexport const trackTimeOnPage = (timeInSeconds: number, page: string) => {\n  trackEvent('time_on_page', 'engagement', page, timeInSeconds, {\n    time_seconds: timeInSeconds,\n    page_path: page,\n  });\n};\n\n// Enhanced E-commerce tracking for affiliate conversions\nexport const trackPurchaseIntent = (\n  productName: string,\n  productId: string,\n  brand: string,\n  price: number,\n  step: 'view_review' | 'click_affiliate' | 'add_to_cart'\n) => {\n  const eventName = step === 'view_review' ? 'view_item' : \n                   step === 'click_affiliate' ? 'select_item' : 'add_to_cart';\n  \n  if (typeof window === 'undefined' || !window.gtag) return;\n  \n  window.gtag('event', eventName, {\n    currency: 'USD',\n    value: price,\n    items: [\n      {\n        item_id: productId,\n        item_name: productName,\n        item_brand: brand,\n        item_category: 'Portable Power Station',\n        price: price,\n        quantity: 1,\n      },\n    ],\n  });\n};\n\n// Track user preferences and behavior\nexport const trackUserPreference = (\n  preferenceType: string,\n  preferenceValue: string\n) => {\n  trackEvent('user_preference', 'personalization', preferenceType, undefined, {\n    preference_type: preferenceType,\n    preference_value: preferenceValue,\n  });\n};\n\n// Conversion tracking for different funnel stages\nexport const trackConversionFunnel = (\n  stage: 'awareness' | 'consideration' | 'decision' | 'action',\n  source: string,\n  productId?: string\n) => {\n  trackEvent('conversion_funnel', 'ecommerce', stage, undefined, {\n    funnel_stage: stage,\n    traffic_source: source,\n    product_id: productId,\n  });\n};\n\n// Track social shares\nexport const trackSocialShare = (\n  platform: string,\n  contentType: string,\n  contentId: string\n) => {\n  trackEvent('social_share', 'engagement', platform, undefined, {\n    social_platform: platform,\n    content_type: contentType,\n    content_id: contentId,\n  });\n};\n\n// Track email interactions\nexport const trackEmailInteraction = (\n  action: 'open' | 'click' | 'unsubscribe',\n  campaignId: string,\n  emailType: string\n) => {\n  trackEvent('email_interaction', 'engagement', action, undefined, {\n    email_action: action,\n    campaign_id: campaignId,\n    email_type: emailType,\n  });\n};\n\n// Performance tracking\nexport const trackPerformanceMetric = (\n  metricName: string,\n  value: number,\n  page: string\n) => {\n  trackEvent('performance_metric', 'technical', metricName, value, {\n    metric_name: metricName,\n    metric_value: value,\n    page_path: page,\n  });\n};\n\n// Error tracking\nexport const trackError = (\n  errorType: string,\n  errorMessage: string,\n  page: string,\n  severity: 'low' | 'medium' | 'high' = 'medium'\n) => {\n  trackEvent('error', 'technical', errorType, undefined, {\n    error_type: errorType,\n    error_message: errorMessage,\n    error_severity: severity,\n    page_path: page,\n  });\n};\n\n// A/B testing tracking\nexport const trackABTest = (\n  testName: string,\n  variant: string,\n  action: 'view' | 'convert'\n) => {\n  trackEvent('ab_test', 'optimization', testName, undefined, {\n    test_name: testName,\n    test_variant: variant,\n    test_action: action,\n  });\n};\n\n// Custom dimensions for enhanced tracking\nexport const setCustomDimensions = (dimensions: Record<string, string>) => {\n  if (typeof window === 'undefined' || !window.gtag) return;\n  \n  window.gtag('config', GA_MEASUREMENT_ID, {\n    custom_map: dimensions,\n  });\n};\n\n// Initialize scroll depth tracking\nexport const initScrollTracking = () => {\n  if (typeof window === 'undefined') return;\n  \n  let maxScroll = 0;\n  const thresholds = [25, 50, 75, 90, 100];\n  const tracked = new Set<number>();\n  \n  const handleScroll = () => {\n    const scrollPercent = Math.round(\n      (window.scrollY / (document.documentElement.scrollHeight - window.innerHeight)) * 100\n    );\n    \n    if (scrollPercent > maxScroll) {\n      maxScroll = scrollPercent;\n      \n      thresholds.forEach(threshold => {\n        if (scrollPercent >= threshold && !tracked.has(threshold)) {\n          tracked.add(threshold);\n          trackScrollDepth(threshold, window.location.pathname);\n        }\n      });\n    }\n  };\n  \n  window.addEventListener('scroll', handleScroll, { passive: true });\n  \n  return () => {\n    window.removeEventListener('scroll', handleScroll);\n  };\n};\n\n// Initialize time tracking\nexport const initTimeTracking = () => {\n  if (typeof window === 'undefined') return;\n  \n  const startTime = Date.now();\n  \n  const trackTime = () => {\n    const timeSpent = Math.round((Date.now() - startTime) / 1000);\n    trackTimeOnPage(timeSpent, window.location.pathname);\n  };\n  \n  // Track time on page visibility change\n  document.addEventListener('visibilitychange', () => {\n    if (document.visibilityState === 'hidden') {\n      trackTime();\n    }\n  });\n  \n  // Track time on page unload\n  window.addEventListener('beforeunload', trackTime);\n  \n  return trackTime;\n};\n"], "names": [], "mappings": "AAAA,sCAAsC;AACtC,6CAA6C;;;;;;;;;;;;;;;;;;;;;;;;;AAUZ;AAA1B,MAAM,oBAAoB,wCAA6C;AAGvE,MAAM,SAAS;IACpB,uCAAmC;;IAAM;IAEzC,+BAA+B;IAC/B,MAAM,SAAS,SAAS,aAAa,CAAC;IACtC,OAAO,GAAG,GAAG,CAAC,4CAA4C,EAAE,mBAAmB;IAC/E,OAAO,KAAK,GAAG;IACf,SAAS,IAAI,CAAC,WAAW,CAAC;IAE1B,gCAAgC;IAChC,OAAO,SAAS,GAAG,OAAO,SAAS,IAAI,EAAE;IACzC,OAAO,IAAI,GAAG,SAAS,KAAK,GAAG,IAAe;QAC5C,OAAO,SAAS,CAAC,IAAI,CAAC;IACxB;IAEA,OAAO,IAAI,CAAC,MAAM,IAAI;IACtB,OAAO,IAAI,CAAC,UAAU,mBAAmB;QACvC,YAAY,SAAS,KAAK;QAC1B,eAAe,OAAO,QAAQ,CAAC,IAAI;IACrC;AACF;AAGO,MAAM,gBAAgB,CAAC,KAAa;IACzC,IAAI,aAAkB,eAAe,CAAC,OAAO,IAAI,EAAE;IAEnD,OAAO,IAAI,CAAC,UAAU,mBAAmB;QACvC,YAAY,SAAS,SAAS,KAAK;QACnC,eAAe;IACjB;AACF;AAGO,MAAM,aAAa,CACxB,QACA,UACA,OACA,OACA;IAEA,IAAI,aAAkB,eAAe,CAAC,OAAO,IAAI,EAAE;IAEnD,OAAO,IAAI,CAAC,SAAS,QAAQ;QAC3B,gBAAgB;QAChB,aAAa;QACb,OAAO;QACP,GAAG,gBAAgB;IACrB;AACF;AAGO,MAAM,sBAAsB,CACjC,aACA,WACA,OACA,OACA;IAEA,WAAW,mBAAmB,aAAa,aAAa,OAAO;QAC7D,YAAY;QACZ,cAAc;QACd,eAAe;QACf,eAAe;QACf,eAAe;QACf,UAAU;IACZ;AACF;AAGO,MAAM,mBAAmB,CAC9B,aACA,WACA,OACA,OACA;IAEA,IAAI,aAAkB,eAAe,CAAC,OAAO,IAAI,EAAE;IAEnD,OAAO,IAAI,CAAC,SAAS,aAAa;QAChC,UAAU;QACV,OAAO;QACP,OAAO;YACL;gBACE,SAAS;gBACT,WAAW;gBACX,YAAY;gBACZ,eAAe;gBACf,OAAO;gBACP,UAAU;YACZ;SACD;IACH;AACF;AAGO,MAAM,wBAAwB,CAAC;IACpC,WAAW,qBAAqB,cAAc,QAAQ,WAAW;QAC/D,eAAe;IACjB;AACF;AAGO,MAAM,cAAc,CAAC,YAAoB;IAC9C,WAAW,UAAU,cAAc,YAAY,cAAc;QAC3D,aAAa;QACb,eAAe;IACjB;AACF;AAGO,MAAM,yBAAyB,CACpC,aACA,WACA,gBACA;IAEA,WAAW,gBAAgB,WAAW,WAAW,OAAO;QACtD,cAAc;QACd,YAAY;IACd;AACF;AAGO,MAAM,mBAAmB,CAAC,YAAoB;IACnD,WAAW,gBAAgB,cAAc,MAAM,YAAY;QACzD,mBAAmB;QACnB,WAAW;IACb;AACF;AAGO,MAAM,kBAAkB,CAAC,eAAuB;IACrD,WAAW,gBAAgB,cAAc,MAAM,eAAe;QAC5D,cAAc;QACd,WAAW;IACb;AACF;AAGO,MAAM,sBAAsB,CACjC,aACA,WACA,OACA,OACA;IAEA,MAAM,YAAY,SAAS,gBAAgB,cAC1B,SAAS,oBAAoB,gBAAgB;IAE9D,IAAI,aAAkB,eAAe,CAAC,OAAO,IAAI,EAAE;IAEnD,OAAO,IAAI,CAAC,SAAS,WAAW;QAC9B,UAAU;QACV,OAAO;QACP,OAAO;YACL;gBACE,SAAS;gBACT,WAAW;gBACX,YAAY;gBACZ,eAAe;gBACf,OAAO;gBACP,UAAU;YACZ;SACD;IACH;AACF;AAGO,MAAM,sBAAsB,CACjC,gBACA;IAEA,WAAW,mBAAmB,mBAAmB,gBAAgB,WAAW;QAC1E,iBAAiB;QACjB,kBAAkB;IACpB;AACF;AAGO,MAAM,wBAAwB,CACnC,OACA,QACA;IAEA,WAAW,qBAAqB,aAAa,OAAO,WAAW;QAC7D,cAAc;QACd,gBAAgB;QAChB,YAAY;IACd;AACF;AAGO,MAAM,mBAAmB,CAC9B,UACA,aACA;IAEA,WAAW,gBAAgB,cAAc,UAAU,WAAW;QAC5D,iBAAiB;QACjB,cAAc;QACd,YAAY;IACd;AACF;AAGO,MAAM,wBAAwB,CACnC,QACA,YACA;IAEA,WAAW,qBAAqB,cAAc,QAAQ,WAAW;QAC/D,cAAc;QACd,aAAa;QACb,YAAY;IACd;AACF;AAGO,MAAM,yBAAyB,CACpC,YACA,OACA;IAEA,WAAW,sBAAsB,aAAa,YAAY,OAAO;QAC/D,aAAa;QACb,cAAc;QACd,WAAW;IACb;AACF;AAGO,MAAM,aAAa,CACxB,WACA,cACA,MACA,WAAsC,QAAQ;IAE9C,WAAW,SAAS,aAAa,WAAW,WAAW;QACrD,YAAY;QACZ,eAAe;QACf,gBAAgB;QAChB,WAAW;IACb;AACF;AAGO,MAAM,cAAc,CACzB,UACA,SACA;IAEA,WAAW,WAAW,gBAAgB,UAAU,WAAW;QACzD,WAAW;QACX,cAAc;QACd,aAAa;IACf;AACF;AAGO,MAAM,sBAAsB,CAAC;IAClC,IAAI,aAAkB,eAAe,CAAC,OAAO,IAAI,EAAE;IAEnD,OAAO,IAAI,CAAC,UAAU,mBAAmB;QACvC,YAAY;IACd;AACF;AAGO,MAAM,qBAAqB;IAChC,uCAAmC;;IAAM;IAEzC,IAAI,YAAY;IAChB,MAAM,aAAa;QAAC;QAAI;QAAI;QAAI;QAAI;KAAI;IACxC,MAAM,UAAU,IAAI;IAEpB,MAAM,eAAe;QACnB,MAAM,gBAAgB,KAAK,KAAK,CAC9B,AAAC,OAAO,OAAO,GAAG,CAAC,SAAS,eAAe,CAAC,YAAY,GAAG,OAAO,WAAW,IAAK;QAGpF,IAAI,gBAAgB,WAAW;YAC7B,YAAY;YAEZ,WAAW,OAAO,CAAC,CAAA;gBACjB,IAAI,iBAAiB,aAAa,CAAC,QAAQ,GAAG,CAAC,YAAY;oBACzD,QAAQ,GAAG,CAAC;oBACZ,iBAAiB,WAAW,OAAO,QAAQ,CAAC,QAAQ;gBACtD;YACF;QACF;IACF;IAEA,OAAO,gBAAgB,CAAC,UAAU,cAAc;QAAE,SAAS;IAAK;IAEhE,OAAO;QACL,OAAO,mBAAmB,CAAC,UAAU;IACvC;AACF;AAGO,MAAM,mBAAmB;IAC9B,uCAAmC;;IAAM;IAEzC,MAAM,YAAY,KAAK,GAAG;IAE1B,MAAM,YAAY;QAChB,MAAM,YAAY,KAAK,KAAK,CAAC,CAAC,KAAK,GAAG,KAAK,SAAS,IAAI;QACxD,gBAAgB,WAAW,OAAO,QAAQ,CAAC,QAAQ;IACrD;IAEA,uCAAuC;IACvC,SAAS,gBAAgB,CAAC,oBAAoB;QAC5C,IAAI,SAAS,eAAe,KAAK,UAAU;YACzC;QACF;IACF;IAEA,4BAA4B;IAC5B,OAAO,gBAAgB,CAAC,gBAAgB;IAExC,OAAO;AACT", "debugId": null}}, {"offset": {"line": 260, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/offgridflux/offgridflux-website/src/components/GoogleAnalytics.tsx"], "sourcesContent": ["// OffGridFlux - Google Analytics Component\n// Client-side analytics initialization and tracking\n\n'use client';\n\nimport { useEffect } from 'react';\nimport { usePathname } from 'next/navigation';\nimport {\n  initGA,\n  trackPageView,\n  initScrollTracking,\n  initTimeTracking,\n  GA_MEASUREMENT_ID\n} from '@/lib/analytics';\n\nexport function GoogleAnalytics() {\n  const pathname = usePathname();\n\n  useEffect(() => {\n    // Initialize Google Analytics\n    initGA();\n\n    // Initialize scroll and time tracking\n    const cleanupScroll = initScrollTracking();\n    const cleanupTime = initTimeTracking();\n\n    return () => {\n      if (cleanupScroll) cleanupScroll();\n      if (cleanupTime) cleanupTime();\n    };\n  }, []);\n\n  useEffect(() => {\n    // Track page views on route changes\n    trackPageView(pathname);\n  }, [pathname]);\n\n  // Only render in production with valid measurement ID\n  if (process.env.NODE_ENV !== 'production' || !GA_MEASUREMENT_ID || GA_MEASUREMENT_ID === 'G-XXXXXXXXXX') {\n    return null;\n  }\n\n  return (\n    <>\n      {/* Google Analytics Script */}\n      <script\n        async\n        src={`https://www.googletagmanager.com/gtag/js?id=${GA_MEASUREMENT_ID}`}\n      />\n      <script\n        dangerouslySetInnerHTML={{\n          __html: `\n            window.dataLayer = window.dataLayer || [];\n            function gtag(){dataLayer.push(arguments);}\n            gtag('js', new Date());\n            gtag('config', '${GA_MEASUREMENT_ID}', {\n              page_title: document.title,\n              page_location: window.location.href,\n            });\n          `,\n        }}\n      />\n    </>\n  );\n}\n\n// Hook for tracking affiliate clicks\nexport function useAffiliateTracking() {\n  const trackClick = (\n    productName: string,\n    productId: string,\n    brand: string,\n    price: number,\n    url: string\n  ) => {\n    // Import analytics functions dynamically to avoid SSR issues\n    import('@/lib/analytics').then(({ trackAffiliateClick }) => {\n      trackAffiliateClick(productName, productId, brand, price, url);\n    });\n  };\n\n  return { trackClick };\n}\n\n// Hook for tracking product views\nexport function useProductTracking() {\n  const trackView = (\n    productName: string,\n    productId: string,\n    brand: string,\n    price: number,\n    category: string\n  ) => {\n    import('@/lib/analytics').then(({ trackProductView }) => {\n      trackProductView(productName, productId, brand, price, category);\n    });\n  };\n\n  return { trackView };\n}\n\n// Hook for tracking newsletter signups\nexport function useNewsletterTracking() {\n  const trackSignup = (source: string) => {\n    import('@/lib/analytics').then(({ trackNewsletterSignup }) => {\n      trackNewsletterSignup(source);\n    });\n  };\n\n  return { trackSignup };\n}\n\n// Hook for tracking search\nexport function useSearchTracking() {\n  const trackSearch = (searchTerm: string, resultsCount: number) => {\n    import('@/lib/analytics').then(({ trackSearch }) => {\n      trackSearch(searchTerm, resultsCount);\n    });\n  };\n\n  return { trackSearch };\n}\n\n// Hook for tracking content engagement\nexport function useContentTracking() {\n  const trackEngagement = (\n    contentType: string,\n    contentId: string,\n    engagementType: string,\n    value?: number\n  ) => {\n    import('@/lib/analytics').then(({ trackContentEngagement }) => {\n      trackContentEngagement(contentType, contentId, engagementType, value);\n    });\n  };\n\n  return { trackEngagement };\n}\n"], "names": [], "mappings": "AAAA,2CAA2C;AAC3C,oDAAoD;;;;;;;;;AAqC9C;;AAjCN;AACA;AACA;;;AAJA;;;;AAYO,SAAS;;IACd,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,8BAA8B;YAC9B,CAAA,GAAA,0HAAA,CAAA,SAAM,AAAD;YAEL,sCAAsC;YACtC,MAAM,gBAAgB,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD;YACvC,MAAM,cAAc,CAAA,GAAA,0HAAA,CAAA,mBAAgB,AAAD;YAEnC;6CAAO;oBACL,IAAI,eAAe;oBACnB,IAAI,aAAa;gBACnB;;QACF;oCAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,oCAAoC;YACpC,CAAA,GAAA,0HAAA,CAAA,gBAAa,AAAD,EAAE;QAChB;oCAAG;QAAC;KAAS;IAEb,sDAAsD;IACtD,wCAAyG;QACvG,OAAO;IACT;;AAwBF;GAjDgB;;QACG,qIAAA,CAAA,cAAW;;;KADd;AAoDT,SAAS;IACd,MAAM,aAAa,CACjB,aACA,WACA,OACA,OACA;QAEA,6DAA6D;QAC7D,2HAA0B,IAAI,CAAC,CAAC,EAAE,mBAAmB,EAAE;YACrD,oBAAoB,aAAa,WAAW,OAAO,OAAO;QAC5D;IACF;IAEA,OAAO;QAAE;IAAW;AACtB;AAGO,SAAS;IACd,MAAM,YAAY,CAChB,aACA,WACA,OACA,OACA;QAEA,2HAA0B,IAAI,CAAC,CAAC,EAAE,gBAAgB,EAAE;YAClD,iBAAiB,aAAa,WAAW,OAAO,OAAO;QACzD;IACF;IAEA,OAAO;QAAE;IAAU;AACrB;AAGO,SAAS;IACd,MAAM,cAAc,CAAC;QACnB,2HAA0B,IAAI,CAAC,CAAC,EAAE,qBAAqB,EAAE;YACvD,sBAAsB;QACxB;IACF;IAEA,OAAO;QAAE;IAAY;AACvB;AAGO,SAAS;IACd,MAAM,cAAc,CAAC,YAAoB;QACvC,2HAA0B,IAAI,CAAC,CAAC,EAAE,WAAW,EAAE;YAC7C,YAAY,YAAY;QAC1B;IACF;IAEA,OAAO;QAAE;IAAY;AACvB;AAGO,SAAS;IACd,MAAM,kBAAkB,CACtB,aACA,WACA,gBACA;QAEA,2HAA0B,IAAI,CAAC,CAAC,EAAE,sBAAsB,EAAE;YACxD,uBAAuB,aAAa,WAAW,gBAAgB;QACjE;IACF;IAEA,OAAO;QAAE;IAAgB;AAC3B", "debugId": null}}, {"offset": {"line": 380, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/offgridflux/offgridflux-website/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js"], "sourcesContent": ["/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return (type.displayName || \"Context\") + \".Provider\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = require(\"next/dist/compiled/react\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      \"react-stack-bottom-frame\": function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React[\"react-stack-bottom-frame\"].bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC,GAGgB;AADjB;AACA,oEACE,AAAC;IACC,SAAS,yBAAyB,IAAI;QACpC,IAAI,QAAQ,MAAM,OAAO;QACzB,IAAI,eAAe,OAAO,MACxB,OAAO,KAAK,QAAQ,KAAK,yBACrB,OACA,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI;QACvC,IAAI,aAAa,OAAO,MAAM,OAAO;QACrC,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;QACX;QACA,IAAI,aAAa,OAAO,MACtB,OACG,aAAa,OAAO,KAAK,GAAG,IAC3B,QAAQ,KAAK,CACX,sHAEJ,KAAK,QAAQ;YAEb,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO,CAAC,KAAK,WAAW,IAAI,SAAS,IAAI;YAC3C,KAAK;gBACH,OAAO,CAAC,KAAK,QAAQ,CAAC,WAAW,IAAI,SAAS,IAAI;YACpD,KAAK;gBACH,IAAI,YAAY,KAAK,MAAM;gBAC3B,OAAO,KAAK,WAAW;gBACvB,QACE,CAAC,AAAC,OAAO,UAAU,WAAW,IAAI,UAAU,IAAI,IAAI,IACnD,OAAO,OAAO,OAAO,gBAAgB,OAAO,MAAM,YAAa;gBAClE,OAAO;YACT,KAAK;gBACH,OACE,AAAC,YAAY,KAAK,WAAW,IAAI,MACjC,SAAS,YACL,YACA,yBAAyB,KAAK,IAAI,KAAK;YAE/C,KAAK;gBACH,YAAY,KAAK,QAAQ;gBACzB,OAAO,KAAK,KAAK;gBACjB,IAAI;oBACF,OAAO,yBAAyB,KAAK;gBACvC,EAAE,OAAO,GAAG,CAAC;QACjB;QACF,OAAO;IACT;IACA,SAAS,mBAAmB,KAAK;QAC/B,OAAO,KAAK;IACd;IACA,SAAS,uBAAuB,KAAK;QACnC,IAAI;YACF,mBAAmB;YACnB,IAAI,2BAA2B,CAAC;QAClC,EAAE,OAAO,GAAG;YACV,2BAA2B,CAAC;QAC9B;QACA,IAAI,0BAA0B;YAC5B,2BAA2B;YAC3B,IAAI,wBAAwB,yBAAyB,KAAK;YAC1D,IAAI,oCACF,AAAC,eAAe,OAAO,UACrB,OAAO,WAAW,IAClB,KAAK,CAAC,OAAO,WAAW,CAAC,IAC3B,MAAM,WAAW,CAAC,IAAI,IACtB;YACF,sBAAsB,IAAI,CACxB,0BACA,4GACA;YAEF,OAAO,mBAAmB;QAC5B;IACF;IACA,SAAS,YAAY,IAAI;QACvB,IAAI,SAAS,qBAAqB,OAAO;QACzC,IACE,aAAa,OAAO,QACpB,SAAS,QACT,KAAK,QAAQ,KAAK,iBAElB,OAAO;QACT,IAAI;YACF,IAAI,OAAO,yBAAyB;YACpC,OAAO,OAAO,MAAM,OAAO,MAAM;QACnC,EAAE,OAAO,GAAG;YACV,OAAO;QACT;IACF;IACA,SAAS;QACP,IAAI,aAAa,qBAAqB,CAAC;QACvC,OAAO,SAAS,aAAa,OAAO,WAAW,QAAQ;IACzD;IACA,SAAS;QACP,OAAO,MAAM;IACf;IACA,SAAS,YAAY,MAAM;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,IAAI,SAAS,OAAO,wBAAwB,CAAC,QAAQ,OAAO,GAAG;YAC/D,IAAI,UAAU,OAAO,cAAc,EAAE,OAAO,CAAC;QAC/C;QACA,OAAO,KAAK,MAAM,OAAO,GAAG;IAC9B;IACA,SAAS,2BAA2B,KAAK,EAAE,WAAW;QACpD,SAAS;YACP,8BACE,CAAC,AAAC,6BAA6B,CAAC,GAChC,QAAQ,KAAK,CACX,2OACA,YACD;QACL;QACA,sBAAsB,cAAc,GAAG,CAAC;QACxC,OAAO,cAAc,CAAC,OAAO,OAAO;YAClC,KAAK;YACL,cAAc,CAAC;QACjB;IACF;IACA,SAAS;QACP,IAAI,gBAAgB,yBAAyB,IAAI,CAAC,IAAI;QACtD,sBAAsB,CAAC,cAAc,IACnC,CAAC,AAAC,sBAAsB,CAAC,cAAc,GAAG,CAAC,GAC3C,QAAQ,KAAK,CACX,8IACD;QACH,gBAAgB,IAAI,CAAC,KAAK,CAAC,GAAG;QAC9B,OAAO,KAAK,MAAM,gBAAgB,gBAAgB;IACpD;IACA,SAAS,aACP,IAAI,EACJ,GAAG,EACH,IAAI,EACJ,MAAM,EACN,KAAK,EACL,KAAK,EACL,UAAU,EACV,SAAS;QAET,OAAO,MAAM,GAAG;QAChB,OAAO;YACL,UAAU;YACV,MAAM;YACN,KAAK;YACL,OAAO;YACP,QAAQ;QACV;QACA,SAAS,CAAC,KAAK,MAAM,OAAO,OAAO,IAAI,IACnC,OAAO,cAAc,CAAC,MAAM,OAAO;YACjC,YAAY,CAAC;YACb,KAAK;QACP,KACA,OAAO,cAAc,CAAC,MAAM,OAAO;YAAE,YAAY,CAAC;YAAG,OAAO;QAAK;QACrE,KAAK,MAAM,GAAG,CAAC;QACf,OAAO,cAAc,CAAC,KAAK,MAAM,EAAE,aAAa;YAC9C,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,eAAe;YACzC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,MAAM,IAAI,CAAC,OAAO,MAAM,CAAC,KAAK,KAAK,GAAG,OAAO,MAAM,CAAC,KAAK;QAChE,OAAO;IACT;IACA,SAAS,WACP,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,MAAM,EACN,IAAI,EACJ,UAAU,EACV,SAAS;QAET,IAAI,WAAW,OAAO,QAAQ;QAC9B,IAAI,KAAK,MAAM,UACb,IAAI,kBACF,IAAI,YAAY,WAAW;YACzB,IACE,mBAAmB,GACnB,mBAAmB,SAAS,MAAM,EAClC,mBAEA,kBAAkB,QAAQ,CAAC,iBAAiB;YAC9C,OAAO,MAAM,IAAI,OAAO,MAAM,CAAC;QACjC,OACE,QAAQ,KAAK,CACX;aAED,kBAAkB;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,WAAW,yBAAyB;YACpC,IAAI,OAAO,OAAO,IAAI,CAAC,QAAQ,MAAM,CAAC,SAAU,CAAC;gBAC/C,OAAO,UAAU;YACnB;YACA,mBACE,IAAI,KAAK,MAAM,GACX,oBAAoB,KAAK,IAAI,CAAC,aAAa,WAC3C;YACN,qBAAqB,CAAC,WAAW,iBAAiB,IAChD,CAAC,AAAC,OACA,IAAI,KAAK,MAAM,GAAG,MAAM,KAAK,IAAI,CAAC,aAAa,WAAW,MAC5D,QAAQ,KAAK,CACX,mOACA,kBACA,UACA,MACA,WAED,qBAAqB,CAAC,WAAW,iBAAiB,GAAG,CAAC,CAAE;QAC7D;QACA,WAAW;QACX,KAAK,MAAM,YACT,CAAC,uBAAuB,WAAY,WAAW,KAAK,QAAS;QAC/D,YAAY,WACV,CAAC,uBAAuB,OAAO,GAAG,GAAI,WAAW,KAAK,OAAO,GAAG,AAAC;QACnE,IAAI,SAAS,QAAQ;YACnB,WAAW,CAAC;YACZ,IAAK,IAAI,YAAY,OACnB,UAAU,YAAY,CAAC,QAAQ,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS;QAChE,OAAO,WAAW;QAClB,YACE,2BACE,UACA,eAAe,OAAO,OAClB,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI,YACjC;QAER,OAAO,aACL,MACA,UACA,MACA,QACA,YACA,UACA,YACA;IAEJ;IACA,SAAS,kBAAkB,IAAI;QAC7B,aAAa,OAAO,QAClB,SAAS,QACT,KAAK,QAAQ,KAAK,sBAClB,KAAK,MAAM,IACX,CAAC,KAAK,MAAM,CAAC,SAAS,GAAG,CAAC;IAC9B;IACA,IAAI,uHACF,qBAAqB,OAAO,GAAG,CAAC,+BAChC,oBAAoB,OAAO,GAAG,CAAC,iBAC/B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC;IACnC,OAAO,GAAG,CAAC;IACX,IAAI,sBAAsB,OAAO,GAAG,CAAC,mBACnC,qBAAqB,OAAO,GAAG,CAAC,kBAChC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC,mBACjC,2BAA2B,OAAO,GAAG,CAAC,wBACtC,kBAAkB,OAAO,GAAG,CAAC,eAC7B,kBAAkB,OAAO,GAAG,CAAC,eAC7B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,2BACpC,uBACE,MAAM,+DAA+D,EACvE,iBAAiB,OAAO,SAAS,CAAC,cAAc,EAChD,cAAc,MAAM,OAAO,EAC3B,aAAa,QAAQ,UAAU,GAC3B,QAAQ,UAAU,GAClB;QACE,OAAO;IACT;IACN,QAAQ;QACN,4BAA4B,SAAU,iBAAiB;YACrD,OAAO;QACT;IACF;IACA,IAAI;IACJ,IAAI,yBAAyB,CAAC;IAC9B,IAAI,yBAAyB,KAAK,CAAC,2BAA2B,CAAC,IAAI,CACjE,OACA;IAEF,IAAI,wBAAwB,WAAW,YAAY;IACnD,IAAI,wBAAwB,CAAC;IAC7B,QAAQ,QAAQ,GAAG;IACnB,QAAQ,MAAM,GAAG,SACf,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,MAAM,EACN,IAAI;QAEJ,IAAI,mBACF,MAAM,qBAAqB,0BAA0B;QACvD,OAAO,WACL,MACA,QACA,UACA,kBACA,QACA,MACA,mBACI,MAAM,2BACN,wBACJ,mBAAmB,WAAW,YAAY,SAAS;IAEvD;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 588, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/offgridflux/offgridflux-website/node_modules/next/dist/compiled/react/jsx-dev-runtime.js"], "sourcesContent": ["'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-jsx-dev-runtime.production.js');\n} else {\n  module.exports = require('./cjs/react-jsx-dev-runtime.development.js');\n}\n"], "names": [], "mappings": "AAEI;AAFJ;AAEA,uCAA2C;;AAE3C,OAAO;IACL,OAAO,OAAO;AAChB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 600, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/offgridflux/offgridflux-website/node_modules/next/navigation.js"], "sourcesContent": ["module.exports = require('./dist/client/components/navigation')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0], "debugId": null}}]}