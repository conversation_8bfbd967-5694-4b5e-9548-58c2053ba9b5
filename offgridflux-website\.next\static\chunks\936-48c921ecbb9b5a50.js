"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[936],{1113:(e,a,r)=>{r.d(a,{AffiliateDisclosure:()=>m});var s=r(5155),t=r(2115),i=r(6126),n=r(285),l=r(9946);let d=(0,l.A)("info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]]),o=(0,l.A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]]);var c=r(4416);function m(){let[e,a]=(0,t.useState)(!0),[r,l]=(0,t.useState)(!1);return e?(0,s.jsx)("div",{className:"bg-primary-50 border-b border-primary-200 relative",children:(0,s.jsx)("div",{className:"max-w-7xl mx-auto px-4 py-3",children:(0,s.jsxs)("div",{className:"flex items-start gap-3",children:[(0,s.jsx)("div",{className:"flex-shrink-0 mt-0.5",children:(0,s.jsx)(d,{className:"w-5 h-5 text-primary-600"})}),(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,s.jsx)("div",{className:"flex items-center gap-2 mb-1",children:(0,s.jsx)(i.E,{variant:"outline",className:"text-xs border-primary-300 text-primary-700",children:"FTC Disclosure"})}),(0,s.jsxs)("div",{className:"text-sm text-primary-800",children:[(0,s.jsx)("p",{className:"font-medium mb-1",children:"Affiliate Disclosure: We earn commissions from qualifying purchases."}),r?(0,s.jsxs)("div",{className:"text-primary-700 space-y-2",children:[(0,s.jsx)("p",{children:"OffGridFlux participates in affiliate programs with Amazon, Jackery, Goal Zero, EcoFlow, and other retailers. When you click on links to products and make a purchase, we may receive a commission at no additional cost to you."}),(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Our Promise:"})," We only recommend products we've personally tested or would use ourselves. Our reviews are based on real-world testing and are not influenced by affiliate relationships. We purchase most products at full retail price for testing."]}),(0,s.jsxs)("p",{children:["Commission earnings help support our independent testing lab and keep our content free for readers. For complete details, see our"," ",(0,s.jsxs)("a",{href:"/affiliate-disclosure",className:"underline hover:no-underline font-medium inline-flex items-center gap-1",children:["full affiliate disclosure policy",(0,s.jsx)(o,{className:"w-3 h-3"})]}),"."]}),(0,s.jsx)("button",{onClick:()=>l(!1),className:"underline hover:no-underline font-medium text-xs",children:"Show less"})]}):(0,s.jsxs)("p",{className:"text-primary-700",children:["As an Amazon Associate and affiliate partner, we earn from qualifying purchases. This doesn't affect our review process or recommendations."," ",(0,s.jsx)("button",{onClick:()=>l(!0),className:"underline hover:no-underline font-medium",children:"Read full disclosure"})]})]})]}),(0,s.jsx)(n.$,{variant:"ghost",size:"sm",onClick:()=>a(!1),className:"flex-shrink-0 h-8 w-8 p-0 text-primary-600 hover:bg-primary-100","aria-label":"Close disclosure",children:(0,s.jsx)(c.A,{className:"w-4 h-4"})})]})})}):null}},4416:(e,a,r)=>{r.d(a,{A:()=>s});let s=(0,r(9946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},5040:(e,a,r)=>{r.d(a,{RuntimeCalculator:()=>x});var s=r(5155),t=r(2115),i=r(9434);function n(e){let{className:a,...r}=e;return(0,s.jsx)("div",{"data-slot":"card",className:(0,i.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",a),...r})}function l(e){let{className:a,...r}=e;return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,i.cn)("px-6",a),...r})}var d=r(2523),o=r(9946);let c=(0,o.A)("smartphone",[["rect",{width:"14",height:"20",x:"5",y:"2",rx:"2",ry:"2",key:"1yt0o3"}],["path",{d:"M12 18h.01",key:"mhygvu"}]]),m=(0,o.A)("laptop",[["path",{d:"M18 5a2 2 0 0 1 2 2v8.526a2 2 0 0 0 .212.897l1.068 2.127a1 1 0 0 1-.9 1.45H3.62a1 1 0 0 1-.9-1.45l1.068-2.127A2 2 0 0 0 4 15.526V7a2 2 0 0 1 2-2z",key:"1pdavp"}],["path",{d:"M20.054 15.987H3.946",key:"14rxg9"}]]),h=(0,o.A)("lightbulb",[["path",{d:"M15 14c.2-1 .7-1.7 1.5-2.5 1-.9 1.5-2.2 1.5-3.5A6 6 0 0 0 6 8c0 1 .2 2.2 1.5 3.5.7.7 1.3 1.5 1.5 2.5",key:"1gvzjb"}],["path",{d:"M9 18h6",key:"x1upvd"}],["path",{d:"M10 22h4",key:"ceow96"}]]),u=(0,o.A)("calculator",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",key:"1nb95v"}],["line",{x1:"8",x2:"16",y1:"6",y2:"6",key:"x4nwl0"}],["line",{x1:"16",x2:"16",y1:"14",y2:"18",key:"wjye3r"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M8 14h.01",key:"6423bh"}],["path",{d:"M12 18h.01",key:"mhygvu"}],["path",{d:"M8 18h.01",key:"lrp35t"}]]),p=[{name:"Smartphone",power:5,icon:c},{name:"Laptop",power:65,icon:m},{name:"LED Light",power:10,icon:h}];function x(e){let{product:a}=e,[r,i]=(0,t.useState)(""),o=e=>(.85*a.capacity_wh/e).toFixed(1);return(0,s.jsx)(n,{children:(0,s.jsxs)(l,{className:"p-6",children:[(0,s.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2",children:[(0,s.jsx)(u,{className:"w-5 h-5"}),"Runtime Calculator"]}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6",children:p.map(e=>{let a=e.icon,r=o(e.power);return(0,s.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4 text-center",children:[(0,s.jsx)(a,{className:"w-8 h-8 text-gray-600 mx-auto mb-2"}),(0,s.jsx)("div",{className:"font-medium text-gray-900",children:e.name}),(0,s.jsxs)("div",{className:"text-sm text-gray-500",children:[e.power,"W"]}),(0,s.jsxs)("div",{className:"text-lg font-bold text-primary-600 mt-2",children:[r," hours"]})]},e.name)})}),(0,s.jsxs)("div",{className:"border-t pt-6",children:[(0,s.jsx)("h4",{className:"font-medium text-gray-900 mb-3",children:"Custom Device"}),(0,s.jsxs)("div",{className:"flex gap-4",children:[(0,s.jsx)(d.p,{type:"number",placeholder:"Power consumption (watts)",value:r,onChange:e=>i(e.target.value),className:"flex-1"}),(0,s.jsx)("div",{className:"flex items-center text-gray-600",children:r&&!isNaN(Number(r))&&Number(r)>0?(0,s.jsxs)("span",{className:"font-medium text-primary-600",children:[o(Number(r))," hours"]}):(0,s.jsx)("span",{children:"Enter watts"})})]})]})]})})}},6126:(e,a,r)=>{r.d(a,{E:()=>d});var s=r(5155);r(2115);var t=r(4624),i=r(2085),n=r(9434);let l=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function d(e){let{className:a,variant:r,asChild:i=!1,...d}=e,o=i?t.DX:"span";return(0,s.jsx)(o,{"data-slot":"badge",className:(0,n.cn)(l({variant:r}),a),...d})}}}]);