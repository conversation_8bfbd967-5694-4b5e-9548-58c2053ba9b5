{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 64, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/offgridflux/offgridflux-website/src/lib/supabase.ts"], "sourcesContent": ["// OffGridFlux - Supabase Client Configuration\n// Handles database connections and real-time subscriptions\n\nimport { createClient as createSupabaseClient } from '@supabase/supabase-js';\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;\n\nif (!supabaseUrl || !supabaseAnonKey) {\n  throw new Error('Missing Supabase environment variables');\n}\n\n// Create Supabase client\nexport const createClient = () => {\n  return createSupabaseClient(supabaseUrl, supabaseAnonKey, {\n    auth: {\n      persistSession: false, // We don't need auth for this public site\n    },\n  });\n};\n\n// Product-related database functions\nexport const getProducts = async (filters?: {\n  brand?: string;\n  category?: string;\n  minCapacity?: number;\n  maxCapacity?: number;\n  maxPrice?: number;\n}) => {\n  const supabase = createClient();\n  let query = supabase\n    .from('product_specs')\n    .select('*')\n    .eq('availability_status', 'In Stock');\n\n  if (filters?.brand) {\n    query = query.eq('brand', filters.brand);\n  }\n  \n  if (filters?.category) {\n    query = query.eq('category', filters.category);\n  }\n  \n  if (filters?.minCapacity) {\n    query = query.gte('capacity_wh', filters.minCapacity);\n  }\n  \n  if (filters?.maxCapacity) {\n    query = query.lte('capacity_wh', filters.maxCapacity);\n  }\n  \n  if (filters?.maxPrice) {\n    query = query.lte('current_price_usd', filters.maxPrice);\n  }\n\n  return query.order('capacity_wh', { ascending: true });\n};\n\nexport const getProductBySlug = async (slug: string) => {\n  const supabase = createClient();\n  return supabase\n    .from('product_specs')\n    .select('*')\n    .eq('slug', slug)\n    .single();\n};\n\nexport const getFeaturedProducts = async (limit: number = 6) => {\n  const supabase = createClient();\n  return supabase\n    .from('product_specs')\n    .select('*')\n    .eq('availability_status', 'In Stock')\n    .not('offgridflux_rating', 'is', null)\n    .order('offgridflux_rating', { ascending: false })\n    .limit(limit);\n};\n\nexport const getBestValueProducts = async (limit: number = 5) => {\n  const supabase = createClient();\n  const { data: products } = await supabase\n    .from('product_specs')\n    .select('*')\n    .eq('availability_status', 'In Stock')\n    .not('current_price_usd', 'is', null)\n    .not('capacity_wh', 'is', null);\n\n  if (!products) return { data: null, error: 'No products found' };\n\n  // Calculate value score and sort\n  const productsWithValue = products\n    .map(product => ({\n      ...product,\n      valueScore: product.capacity_wh / parseFloat(product.current_price_usd)\n    }))\n    .sort((a, b) => b.valueScore - a.valueScore)\n    .slice(0, limit);\n\n  return { data: productsWithValue, error: null };\n};\n\n// Real-time price updates using Server-Sent Events\nexport const subscribeToPriceUpdates = (callback: (product: Product) => void) => {\n  const supabase = createClient();\n  \n  return supabase\n    .channel('price-updates')\n    .on(\n      'postgres_changes',\n      {\n        event: 'UPDATE',\n        schema: 'public',\n        table: 'product_specs',\n        filter: 'current_price_usd=neq.null'\n      },\n      (payload) => {\n        callback(payload.new as Product);\n      }\n    )\n    .subscribe();\n};\n\n// Search functionality\nexport const searchProducts = async (query: string, limit: number = 10) => {\n  const supabase = createClient();\n  \n  return supabase\n    .from('product_specs')\n    .select('*')\n    .eq('availability_status', 'In Stock')\n    .or(`product_name.ilike.%${query}%,brand.ilike.%${query}%,model.ilike.%${query}%`)\n    .limit(limit);\n};\n\n// Analytics functions\nexport const trackProductView = async (productId: number, userAgent?: string) => {\n  const supabase = createClient();\n  \n  return supabase\n    .from('product_views')\n    .insert({\n      product_id: productId,\n      viewed_at: new Date().toISOString(),\n      user_agent: userAgent\n    });\n};\n\nexport const trackAffiliateClick = async (productId: number, affiliateUrl: string) => {\n  const supabase = createClient();\n  \n  return supabase\n    .from('affiliate_clicks')\n    .insert({\n      product_id: productId,\n      affiliate_url: affiliateUrl,\n      clicked_at: new Date().toISOString()\n    });\n};\n\n// Type definitions for better TypeScript support\nexport interface Product {\n  id: number;\n  created_at: string;\n  updated_at: string;\n  brand: string;\n  model: string;\n  product_name: string;\n  category: string;\n  series?: string;\n  capacity_wh: number;\n  max_output_w: number;\n  peak_output_w?: number;\n  battery_type?: string;\n  weight_lbs?: number;\n  msrp_usd?: number;\n  current_price_usd?: number;\n  discount_percent?: number;\n  ac_outlets?: number;\n  usb_a_ports?: number;\n  usb_c_ports?: number;\n  usb_c_max_w?: number;\n  dc_ports?: number;\n  ups_capability?: boolean;\n  app_control?: boolean;\n  expandable?: boolean;\n  max_expansion_wh?: number;\n  warranty_years?: number;\n  product_image_url?: string;\n  product_gallery_urls?: string[];\n  key_features?: string[];\n  use_cases?: string[];\n  affiliate_url?: string;\n  manufacturer_url?: string;\n  slug: string;\n  meta_title?: string;\n  meta_description?: string;\n  keywords?: string[];\n  tested_by_offgridflux?: boolean;\n  offgridflux_rating?: number;\n  pros?: string[];\n  cons?: string[];\n  availability_status: string;\n  runtime_smartphone_hours?: number;\n  runtime_laptop_hours?: number;\n  runtime_mini_fridge_hours?: number;\n  runtime_cpap_hours?: number;\n  runtime_led_lights_hours?: number;\n}\n\nexport interface ProductFilters {\n  brand?: string;\n  category?: string;\n  minCapacity?: number;\n  maxCapacity?: number;\n  maxPrice?: number;\n  minRating?: number;\n  features?: string[];\n}\n"], "names": [], "mappings": "AAAA,8CAA8C;AAC9C,2DAA2D;;;;;;;;;;;;AAE3D;;AAEA,MAAM;AACN,MAAM;AAEN,uCAAsC;;AAEtC;AAGO,MAAM,eAAe;IAC1B,OAAO,CAAA,GAAA,uLAAA,CAAA,eAAoB,AAAD,EAAE,aAAa,iBAAiB;QACxD,MAAM;YACJ,gBAAgB;QAClB;IACF;AACF;AAGO,MAAM,cAAc,OAAO;IAOhC,MAAM,WAAW;IACjB,IAAI,QAAQ,SACT,IAAI,CAAC,iBACL,MAAM,CAAC,KACP,EAAE,CAAC,uBAAuB;IAE7B,IAAI,SAAS,OAAO;QAClB,QAAQ,MAAM,EAAE,CAAC,SAAS,QAAQ,KAAK;IACzC;IAEA,IAAI,SAAS,UAAU;QACrB,QAAQ,MAAM,EAAE,CAAC,YAAY,QAAQ,QAAQ;IAC/C;IAEA,IAAI,SAAS,aAAa;QACxB,QAAQ,MAAM,GAAG,CAAC,eAAe,QAAQ,WAAW;IACtD;IAEA,IAAI,SAAS,aAAa;QACxB,QAAQ,MAAM,GAAG,CAAC,eAAe,QAAQ,WAAW;IACtD;IAEA,IAAI,SAAS,UAAU;QACrB,QAAQ,MAAM,GAAG,CAAC,qBAAqB,QAAQ,QAAQ;IACzD;IAEA,OAAO,MAAM,KAAK,CAAC,eAAe;QAAE,WAAW;IAAK;AACtD;AAEO,MAAM,mBAAmB,OAAO;IACrC,MAAM,WAAW;IACjB,OAAO,SACJ,IAAI,CAAC,iBACL,MAAM,CAAC,KACP,EAAE,CAAC,QAAQ,MACX,MAAM;AACX;AAEO,MAAM,sBAAsB,OAAO,QAAgB,CAAC;IACzD,MAAM,WAAW;IACjB,OAAO,SACJ,IAAI,CAAC,iBACL,MAAM,CAAC,KACP,EAAE,CAAC,uBAAuB,YAC1B,GAAG,CAAC,sBAAsB,MAAM,MAChC,KAAK,CAAC,sBAAsB;QAAE,WAAW;IAAM,GAC/C,KAAK,CAAC;AACX;AAEO,MAAM,uBAAuB,OAAO,QAAgB,CAAC;IAC1D,MAAM,WAAW;IACjB,MAAM,EAAE,MAAM,QAAQ,EAAE,GAAG,MAAM,SAC9B,IAAI,CAAC,iBACL,MAAM,CAAC,KACP,EAAE,CAAC,uBAAuB,YAC1B,GAAG,CAAC,qBAAqB,MAAM,MAC/B,GAAG,CAAC,eAAe,MAAM;IAE5B,IAAI,CAAC,UAAU,OAAO;QAAE,MAAM;QAAM,OAAO;IAAoB;IAE/D,iCAAiC;IACjC,MAAM,oBAAoB,SACvB,GAAG,CAAC,CAAA,UAAW,CAAC;YACf,GAAG,OAAO;YACV,YAAY,QAAQ,WAAW,GAAG,WAAW,QAAQ,iBAAiB;QACxE,CAAC,GACA,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,UAAU,GAAG,EAAE,UAAU,EAC1C,KAAK,CAAC,GAAG;IAEZ,OAAO;QAAE,MAAM;QAAmB,OAAO;IAAK;AAChD;AAGO,MAAM,0BAA0B,CAAC;IACtC,MAAM,WAAW;IAEjB,OAAO,SACJ,OAAO,CAAC,iBACR,EAAE,CACD,oBACA;QACE,OAAO;QACP,QAAQ;QACR,OAAO;QACP,QAAQ;IACV,GACA,CAAC;QACC,SAAS,QAAQ,GAAG;IACtB,GAED,SAAS;AACd;AAGO,MAAM,iBAAiB,OAAO,OAAe,QAAgB,EAAE;IACpE,MAAM,WAAW;IAEjB,OAAO,SACJ,IAAI,CAAC,iBACL,MAAM,CAAC,KACP,EAAE,CAAC,uBAAuB,YAC1B,EAAE,CAAC,CAAC,oBAAoB,EAAE,MAAM,eAAe,EAAE,MAAM,eAAe,EAAE,MAAM,CAAC,CAAC,EAChF,KAAK,CAAC;AACX;AAGO,MAAM,mBAAmB,OAAO,WAAmB;IACxD,MAAM,WAAW;IAEjB,OAAO,SACJ,IAAI,CAAC,iBACL,MAAM,CAAC;QACN,YAAY;QACZ,WAAW,IAAI,OAAO,WAAW;QACjC,YAAY;IACd;AACJ;AAEO,MAAM,sBAAsB,OAAO,WAAmB;IAC3D,MAAM,WAAW;IAEjB,OAAO,SACJ,IAAI,CAAC,oBACL,MAAM,CAAC;QACN,YAAY;QACZ,eAAe;QACf,YAAY,IAAI,OAAO,WAAW;IACpC;AACJ", "debugId": null}}, {"offset": {"line": 177, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/offgridflux/offgridflux-website/src/components/ProductCard.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ProductCard = registerClientReference(\n    function() { throw new Error(\"Attempted to call ProductCard() from the server but ProductCard is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ProductCard.tsx <module evaluation>\",\n    \"ProductCard\",\n);\nexport const ProductCardSkeleton = registerClientReference(\n    function() { throw new Error(\"Attempted to call ProductCardSkeleton() from the server but ProductCardSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ProductCard.tsx <module evaluation>\",\n    \"ProductCardSkeleton\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,cAAc,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,gEACA;AAEG,MAAM,sBAAsB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACrD;IAAa,MAAM,IAAI,MAAM;AAAsP,GACnR,gEACA", "debugId": null}}, {"offset": {"line": 195, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/offgridflux/offgridflux-website/src/components/ProductCard.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ProductCard = registerClientReference(\n    function() { throw new Error(\"Attempted to call ProductCard() from the server but ProductCard is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ProductCard.tsx\",\n    \"ProductCard\",\n);\nexport const ProductCardSkeleton = registerClientReference(\n    function() { throw new Error(\"Attempted to call ProductCardSkeleton() from the server but ProductCardSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ProductCard.tsx\",\n    \"ProductCardSkeleton\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,cAAc,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,4CACA;AAEG,MAAM,sBAAsB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACrD;IAAa,MAAM,IAAI,MAAM;AAAsP,GACnR,4CACA", "debugId": null}}, {"offset": {"line": 213, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 223, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/offgridflux/offgridflux-website/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 239, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/offgridflux/offgridflux-website/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 296, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/offgridflux/offgridflux-website/src/components/Hero.tsx"], "sourcesContent": ["// OffGridFlux - Hero Component\n// Modern hero section for the homepage\n\nimport { <PERSON><PERSON> } from '@/components/ui/button';\nimport { <PERSON>ap, Shield, Users, Star, ArrowRight, Clock, CheckCircle } from 'lucide-react';\n\nexport function Hero() {\n  return (\n    <section className=\"relative min-h-screen bg-white overflow-hidden\">\n      {/* Background Elements */}\n      <div className=\"absolute inset-0\">\n        {/* Gradient Orbs */}\n        <div className=\"absolute top-20 left-10 w-72 h-72 bg-primary-500/20 rounded-full blur-3xl\"></div>\n        <div className=\"absolute top-40 right-20 w-96 h-96 bg-energy-500/15 rounded-full blur-3xl\"></div>\n        <div className=\"absolute bottom-20 left-1/3 w-80 h-80 bg-warning-500/10 rounded-full blur-3xl\"></div>\n\n        {/* Grid Pattern */}\n        <div className=\"absolute inset-0 bg-[linear-gradient(to_right,#f1f5f9_1px,transparent_1px),linear-gradient(to_bottom,#f1f5f9_1px,transparent_1px)] bg-[size:4rem_4rem] opacity-30\"></div>\n      </div>\n\n      {/* Main Content */}\n      <div className=\"relative z-10 container mx-auto px-4 pt-20 pb-20\">\n        <div className=\"max-w-7xl mx-auto\">\n          {/* Header Section */}\n          <div className=\"text-center mb-16\">\n            {/* Trust Badges */}\n            <div className=\"flex flex-wrap justify-center gap-3 mb-8\">\n              <div className=\"flex items-center gap-2 bg-green-50 text-green-700 rounded-full px-4 py-2 border border-green-200\">\n                <CheckCircle className=\"w-4 h-4\" />\n                <span className=\"text-sm font-medium\">FTC Compliant</span>\n              </div>\n              <div className=\"flex items-center gap-2 bg-yellow-50 text-yellow-700 rounded-full px-4 py-2 border border-yellow-200\">\n                <Star className=\"w-4 h-4\" />\n                <span className=\"text-sm font-medium\">Expert Tested</span>\n              </div>\n              <div className=\"flex items-center gap-2 bg-blue-50 text-blue-700 rounded-full px-4 py-2 border border-blue-200\">\n                <Users className=\"w-4 h-4\" />\n                <span className=\"text-sm font-medium\">Real Reviews</span>\n              </div>\n            </div>\n\n            {/* Main Headline */}\n            <h1 className=\"text-5xl md:text-6xl lg:text-7xl font-bold text-neutral-900 leading-tight mb-6\">\n              Unbiased Power Station\n              <br />\n              <span className=\"text-transparent bg-clip-text bg-gradient-to-r from-primary-600 to-energy-600\">Reviews & Testing</span>\n            </h1>\n\n            <p className=\"text-xl md:text-2xl text-neutral-600 max-w-3xl mx-auto mb-12 leading-relaxed\">\n              Real-world testing and honest reviews to help you choose the perfect portable power solution for your off-grid adventures.\n            </p>\n\n            {/* CTA Buttons */}\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center mb-16\">\n              <Button size=\"lg\" className=\"bg-primary-600 hover:bg-primary-700 text-white px-8 py-4 text-lg font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300\">\n                View Top Rated Power Stations\n                <ArrowRight className=\"ml-2 w-5 h-5\" />\n              </Button>\n              <Button variant=\"outline\" size=\"lg\" className=\"border-neutral-300 text-neutral-700 hover:bg-neutral-50 px-8 py-4 text-lg font-semibold rounded-xl\">\n                Browse All Reviews\n              </Button>\n            </div>\n          </div>\n\n          {/* Stats Grid */}\n          <div className=\"grid grid-cols-2 md:grid-cols-4 gap-8 mb-16\">\n            <div className=\"text-center p-6 bg-white rounded-2xl shadow-lg border border-neutral-200 hover:shadow-xl transition-shadow duration-300\">\n              <div className=\"w-12 h-12 bg-primary-100 rounded-xl flex items-center justify-center mx-auto mb-4\">\n                <Zap className=\"w-6 h-6 text-primary-600\" />\n              </div>\n              <div className=\"text-3xl font-bold text-neutral-900 mb-2\">17+</div>\n              <div className=\"text-neutral-600 font-medium\">Products Tested</div>\n              <div className=\"text-neutral-500 text-sm mt-1\">Comprehensive testing</div>\n            </div>\n\n            <div className=\"text-center p-6 bg-white rounded-2xl shadow-lg border border-neutral-200 hover:shadow-xl transition-shadow duration-300\">\n              <div className=\"w-12 h-12 bg-energy-100 rounded-xl flex items-center justify-center mx-auto mb-4\">\n                <Clock className=\"w-6 h-6 text-energy-600\" />\n              </div>\n              <div className=\"text-3xl font-bold text-neutral-900 mb-2\">30+</div>\n              <div className=\"text-neutral-600 font-medium\">Days Per Test</div>\n              <div className=\"text-neutral-500 text-sm mt-1\">Thorough evaluation</div>\n            </div>\n\n            <div className=\"text-center p-6 bg-white rounded-2xl shadow-lg border border-neutral-200 hover:shadow-xl transition-shadow duration-300\">\n              <div className=\"w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center mx-auto mb-4\">\n                <Shield className=\"w-6 h-6 text-green-600\" />\n              </div>\n              <div className=\"text-3xl font-bold text-neutral-900 mb-2\">100%</div>\n              <div className=\"text-neutral-600 font-medium\">Independent</div>\n              <div className=\"text-neutral-500 text-sm mt-1\">No manufacturer bias</div>\n            </div>\n\n            <div className=\"text-center p-6 bg-white rounded-2xl shadow-lg border border-neutral-200 hover:shadow-xl transition-shadow duration-300\">\n              <div className=\"w-12 h-12 bg-warning-100 rounded-xl flex items-center justify-center mx-auto mb-4\">\n                <Users className=\"w-6 h-6 text-warning-600\" />\n              </div>\n              <div className=\"text-3xl font-bold text-neutral-900 mb-2\">10K+</div>\n              <div className=\"text-neutral-600 font-medium\">Monthly Readers</div>\n              <div className=\"text-neutral-500 text-sm mt-1\">Trusted community</div>\n            </div>\n          </div>\n\n          {/* Featured Review Card */}\n          <div className=\"max-w-4xl mx-auto\">\n            <div className=\"bg-gradient-to-r from-primary-50 to-energy-50 rounded-3xl p-8 md:p-12 border border-primary-200 shadow-xl\">\n              <div className=\"text-center mb-8\">\n                <div className=\"flex justify-center mb-4\">\n                  {[...Array(5)].map((_, i) => (\n                    <Star key={i} className=\"w-6 h-6 text-yellow-400 fill-current\" />\n                  ))}\n                </div>\n                <blockquote className=\"text-xl md:text-2xl text-neutral-700 italic font-light leading-relaxed\">\n                  &ldquo;OffGridFlux provides the most thorough and unbiased portable power station reviews. Their real-world testing helped me choose the perfect setup for my off-grid cabin.&rdquo;\n                </blockquote>\n                <div className=\"mt-6 flex items-center justify-center gap-4\">\n                  <div className=\"w-12 h-12 bg-primary-200 rounded-full flex items-center justify-center\">\n                    <span className=\"text-primary-700 font-bold\">SM</span>\n                  </div>\n                  <div className=\"text-left\">\n                    <div className=\"font-semibold text-neutral-900\">Sarah M.</div>\n                    <div className=\"text-neutral-600 text-sm\">Verified Reader</div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": "AAAA,+BAA+B;AAC/B,uCAAuC;;;;;AAEvC;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;AAEO,SAAS;IACd,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCAGf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAIjB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,2NAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;8DACvB,8OAAC;oDAAK,WAAU;8DAAsB;;;;;;;;;;;;sDAExC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,8OAAC;oDAAK,WAAU;8DAAsB;;;;;;;;;;;;sDAExC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,8OAAC;oDAAK,WAAU;8DAAsB;;;;;;;;;;;;;;;;;;8CAK1C,8OAAC;oCAAG,WAAU;;wCAAiF;sDAE7F,8OAAC;;;;;sDACD,8OAAC;4CAAK,WAAU;sDAAgF;;;;;;;;;;;;8CAGlG,8OAAC;oCAAE,WAAU;8CAA+E;;;;;;8CAK5F,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kIAAA,CAAA,SAAM;4CAAC,MAAK;4CAAK,WAAU;;gDAAkJ;8DAE5K,8OAAC,kNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;sDAExB,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,MAAK;4CAAK,WAAU;sDAAqG;;;;;;;;;;;;;;;;;;sCAOvJ,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,gMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;;;;;;sDAEjB,8OAAC;4CAAI,WAAU;sDAA2C;;;;;;sDAC1D,8OAAC;4CAAI,WAAU;sDAA+B;;;;;;sDAC9C,8OAAC;4CAAI,WAAU;sDAAgC;;;;;;;;;;;;8CAGjD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;sDAEnB,8OAAC;4CAAI,WAAU;sDAA2C;;;;;;sDAC1D,8OAAC;4CAAI,WAAU;sDAA+B;;;;;;sDAC9C,8OAAC;4CAAI,WAAU;sDAAgC;;;;;;;;;;;;8CAGjD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;;;;;;sDAEpB,8OAAC;4CAAI,WAAU;sDAA2C;;;;;;sDAC1D,8OAAC;4CAAI,WAAU;sDAA+B;;;;;;sDAC9C,8OAAC;4CAAI,WAAU;sDAAgC;;;;;;;;;;;;8CAGjD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;sDAEnB,8OAAC;4CAAI,WAAU;sDAA2C;;;;;;sDAC1D,8OAAC;4CAAI,WAAU;sDAA+B;;;;;;sDAC9C,8OAAC;4CAAI,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;sCAKnD,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACZ;mDAAI,MAAM;6CAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC,kMAAA,CAAA,OAAI;oDAAS,WAAU;mDAAb;;;;;;;;;;sDAGf,8OAAC;4CAAW,WAAU;sDAAyE;;;;;;sDAG/F,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAK,WAAU;kEAA6B;;;;;;;;;;;8DAE/C,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEAAiC;;;;;;sEAChD,8OAAC;4DAAI,WAAU;sEAA2B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU9D", "debugId": null}}, {"offset": {"line": 834, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/offgridflux/offgridflux-website/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 931, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/offgridflux/offgridflux-website/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 977, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/offgridflux/offgridflux-website/src/components/CategoryGrid.tsx"], "sourcesContent": ["// OffGridFlux - Category Grid Component\n// Displays product categories in a grid layout\n\nimport Link from 'next/link';\nimport { Card, CardContent } from '@/components/ui/card';\nimport { Badge } from '@/components/ui/badge';\nimport { Zap, Sun, Home, ArrowRight } from 'lucide-react';\n\ninterface Category {\n  slug: string;\n  title: string;\n  description: string;\n  icon: React.ComponentType<{ className?: string }>;\n  gradient: string;\n  iconBg: string;\n  productCount: number;\n  priceRange: string;\n  featured?: boolean;\n}\n\nconst categories: Category[] = [\n  {\n    slug: 'portable-power-stations',\n    title: 'Portable Power Stations',\n    description: 'Compact power for camping, emergencies, and outdoor adventures',\n    icon: Zap,\n    gradient: 'from-blue-50 to-blue-100',\n    iconBg: 'bg-primary-600',\n    productCount: 12,\n    priceRange: '$149 - $999',\n    featured: true,\n  },\n  {\n    slug: 'solar-generators',\n    title: 'Solar Generators',\n    description: 'Renewable power solutions with integrated solar panels',\n    icon: Sun,\n    gradient: 'from-yellow-50 to-yellow-100',\n    iconBg: 'bg-warning-600',\n    productCount: 8,\n    priceRange: '$299 - $1,499',\n  },\n  {\n    slug: 'home-battery-systems',\n    title: 'Home Battery Systems',\n    description: 'Whole home backup power and grid independence solutions',\n    icon: Home,\n    gradient: 'from-green-50 to-green-100',\n    iconBg: 'bg-energy-600',\n    productCount: 5,\n    priceRange: '$2,999 - $5,799',\n  },\n];\n\nexport function CategoryGrid() {\n  return (\n    <section className=\"py-20 bg-neutral-50 border-t border-neutral-200\">\n      <div className=\"max-w-6xl mx-auto px-4\">\n        {/* Section Header */}\n        <div className=\"text-center mb-16\">\n          <div className=\"inline-flex items-center gap-2 bg-neutral-100 text-neutral-700 rounded-full px-4 py-2 text-sm font-medium mb-4\">\n            <span className=\"w-2 h-2 bg-neutral-500 rounded-full\"></span>\n            Browse Categories\n          </div>\n          <h2 className=\"text-4xl font-bold text-neutral-900 mb-6\">\n            Find Your Perfect Power Solution\n          </h2>\n          <p className=\"text-xl text-neutral-600 max-w-3xl mx-auto leading-relaxed\">\n            Explore our carefully curated categories to find the ideal power solution\n            for your specific needs, budget, and use case.\n          </p>\n        </div>\n\n        {/* Categories Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n          {categories.map((category) => {\n            const IconComponent = category.icon;\n            \n            return (\n              <Link \n                key={category.slug} \n                href={`/category/${category.slug}`}\n                className=\"group block\"\n              >\n                <Card className=\"h-full hover:shadow-lg transition-all duration-300 group-hover:scale-105 border-0 shadow-soft\">\n                  <CardContent className={`p-0 bg-gradient-to-br ${category.gradient} rounded-lg overflow-hidden`}>\n                    <div className=\"p-8 relative\">\n                      {/* Featured Badge */}\n                      {category.featured && (\n                        <Badge className=\"absolute top-4 right-4 bg-primary-600 text-white\">\n                          Most Popular\n                        </Badge>\n                      )}\n                      \n                      {/* Icon */}\n                      <div className={`w-16 h-16 ${category.iconBg} rounded-full flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300`}>\n                        <IconComponent className=\"w-8 h-8 text-white\" />\n                      </div>\n                      \n                      {/* Content */}\n                      <h3 className=\"text-xl font-semibold text-gray-900 mb-3 group-hover:text-primary-600 transition-colors\">\n                        {category.title}\n                      </h3>\n                      \n                      <p className=\"text-gray-600 mb-4 leading-relaxed\">\n                        {category.description}\n                      </p>\n                      \n                      {/* Stats */}\n                      <div className=\"flex items-center justify-between text-sm text-gray-500 mb-4\">\n                        <span>{category.productCount} products</span>\n                        <span>{category.priceRange}</span>\n                      </div>\n                      \n                      {/* CTA */}\n                      <div className=\"flex items-center text-primary-600 font-medium group-hover:text-primary-700 transition-colors\">\n                        <span>Explore Category</span>\n                        <ArrowRight className=\"w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform\" />\n                      </div>\n                    </div>\n                  </CardContent>\n                </Card>\n              </Link>\n            );\n          })}\n        </div>\n\n        {/* Bottom CTA */}\n        <div className=\"text-center mt-12\">\n          <div className=\"bg-gray-50 rounded-lg p-8\">\n            <h3 className=\"text-xl font-semibold text-gray-900 mb-4\">\n              Not sure which category is right for you?\n            </h3>\n            <p className=\"text-gray-600 mb-6 max-w-2xl mx-auto\">\n              Our buying guide helps you choose the perfect power solution based on your specific needs,\n              budget, and use cases.\n            </p>\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n              <Link \n                href=\"/guides/buying-guide\"\n                className=\"inline-flex items-center justify-center bg-primary-600 hover:bg-primary-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-200\"\n              >\n                View Buying Guide\n              </Link>\n              <Link \n                href=\"/comparison\"\n                className=\"inline-flex items-center justify-center border border-gray-300 hover:border-gray-400 text-gray-700 font-semibold py-3 px-6 rounded-lg transition-colors duration-200\"\n              >\n                Compare All Products\n              </Link>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": "AAAA,wCAAwC;AACxC,+CAA+C;;;;;AAE/C;AACA;AACA;AACA;AAAA;AAAA;AAAA;;;;;;AAcA,MAAM,aAAyB;IAC7B;QACE,MAAM;QACN,OAAO;QACP,aAAa;QACb,MAAM,gMAAA,CAAA,MAAG;QACT,UAAU;QACV,QAAQ;QACR,cAAc;QACd,YAAY;QACZ,UAAU;IACZ;IACA;QACE,MAAM;QACN,OAAO;QACP,aAAa;QACb,MAAM,gMAAA,CAAA,MAAG;QACT,UAAU;QACV,QAAQ;QACR,cAAc;QACd,YAAY;IACd;IACA;QACE,MAAM;QACN,OAAO;QACP,aAAa;QACb,MAAM,mMAAA,CAAA,OAAI;QACV,UAAU;QACV,QAAQ;QACR,cAAc;QACd,YAAY;IACd;CACD;AAEM,SAAS;IACd,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAU;;;;;;gCAA6C;;;;;;;sCAG/D,8OAAC;4BAAG,WAAU;sCAA2C;;;;;;sCAGzD,8OAAC;4BAAE,WAAU;sCAA6D;;;;;;;;;;;;8BAO5E,8OAAC;oBAAI,WAAU;8BACZ,WAAW,GAAG,CAAC,CAAC;wBACf,MAAM,gBAAgB,SAAS,IAAI;wBAEnC,qBACE,8OAAC,4JAAA,CAAA,UAAI;4BAEH,MAAM,CAAC,UAAU,EAAE,SAAS,IAAI,EAAE;4BAClC,WAAU;sCAEV,cAAA,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAW,CAAC,sBAAsB,EAAE,SAAS,QAAQ,CAAC,2BAA2B,CAAC;8CAC7F,cAAA,8OAAC;wCAAI,WAAU;;4CAEZ,SAAS,QAAQ,kBAChB,8OAAC,iIAAA,CAAA,QAAK;gDAAC,WAAU;0DAAmD;;;;;;0DAMtE,8OAAC;gDAAI,WAAW,CAAC,UAAU,EAAE,SAAS,MAAM,CAAC,2GAA2G,CAAC;0DACvJ,cAAA,8OAAC;oDAAc,WAAU;;;;;;;;;;;0DAI3B,8OAAC;gDAAG,WAAU;0DACX,SAAS,KAAK;;;;;;0DAGjB,8OAAC;gDAAE,WAAU;0DACV,SAAS,WAAW;;;;;;0DAIvB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;4DAAM,SAAS,YAAY;4DAAC;;;;;;;kEAC7B,8OAAC;kEAAM,SAAS,UAAU;;;;;;;;;;;;0DAI5B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;kEAAK;;;;;;kEACN,8OAAC,kNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;2BArCzB,SAAS,IAAI;;;;;oBA4CxB;;;;;;8BAIF,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA2C;;;;;;0CAGzD,8OAAC;gCAAE,WAAU;0CAAuC;;;;;;0CAIpD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf", "debugId": null}}, {"offset": {"line": 1281, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/offgridflux/offgridflux-website/src/components/NewsletterSignup.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const NewsletterSignup = registerClientReference(\n    function() { throw new Error(\"Attempted to call NewsletterSignup() from the server but NewsletterSignup is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/NewsletterSignup.tsx <module evaluation>\",\n    \"NewsletterSignup\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,mBAAmB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,qEACA", "debugId": null}}, {"offset": {"line": 1295, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/offgridflux/offgridflux-website/src/components/NewsletterSignup.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const NewsletterSignup = registerClientReference(\n    function() { throw new Error(\"Attempted to call NewsletterSignup() from the server but NewsletterSignup is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/NewsletterSignup.tsx\",\n    \"NewsletterSignup\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,mBAAmB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,iDACA", "debugId": null}}, {"offset": {"line": 1309, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1319, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/offgridflux/offgridflux-website/src/components/TrustSignals.tsx"], "sourcesContent": ["// OffGridFlux - Trust Signals Component\n// Displays credibility indicators and social proof\n\nimport { Badge } from '@/components/ui/badge';\nimport { Shield, Award, Users, Clock, CheckCircle, Star } from 'lucide-react';\n\ninterface TrustMetric {\n  icon: React.ComponentType<{ className?: string }>;\n  value: string;\n  label: string;\n  description?: string;\n}\n\nconst trustMetrics: TrustMetric[] = [\n  {\n    icon: Award,\n    value: '17+',\n    label: 'Products Tested',\n    description: 'Comprehensive real-world testing'\n  },\n  {\n    icon: Clock,\n    value: '30+',\n    label: 'Days Per Test',\n    description: 'Thorough evaluation period'\n  },\n  {\n    icon: Shield,\n    value: '100%',\n    label: 'Independent',\n    description: 'No manufacturer influence'\n  },\n  {\n    icon: Users,\n    value: '10K+',\n    label: 'Monthly Readers',\n    description: 'Trusted by the community'\n  }\n];\n\nconst certifications = [\n  {\n    name: 'FTC Compliant',\n    description: 'Full affiliate disclosure',\n    icon: CheckCircle,\n    color: 'text-green-600'\n  },\n  {\n    name: 'Expert Tested',\n    description: 'Professional evaluation',\n    icon: Star,\n    color: 'text-yellow-600'\n  },\n  {\n    name: 'Real Reviews',\n    description: 'Authentic user feedback',\n    icon: Users,\n    color: 'text-blue-600'\n  }\n];\n\nexport function TrustSignals() {\n  return (\n    <section className=\"py-12 bg-white border-b border-gray-100\">\n      <div className=\"max-w-6xl mx-auto px-4\">\n        \n        {/* Main Trust Metrics */}\n        <div className=\"grid grid-cols-2 md:grid-cols-4 gap-8 mb-12\">\n          {trustMetrics.map((metric, index) => {\n            const IconComponent = metric.icon;\n            \n            return (\n              <div key={index} className=\"text-center group\">\n                <div className=\"inline-flex items-center justify-center w-12 h-12 bg-primary-100 rounded-full mb-3 group-hover:bg-primary-200 transition-colors\">\n                  <IconComponent className=\"w-6 h-6 text-primary-600\" />\n                </div>\n                <div className=\"text-3xl font-bold text-gray-900 mb-1\">\n                  {metric.value}\n                </div>\n                <div className=\"text-gray-600 font-medium mb-1\">\n                  {metric.label}\n                </div>\n                {metric.description && (\n                  <div className=\"text-sm text-gray-500\">\n                    {metric.description}\n                  </div>\n                )}\n              </div>\n            );\n          })}\n        </div>\n\n        {/* Certifications & Badges */}\n        <div className=\"flex flex-wrap justify-center items-center gap-6 py-8 border-t border-gray-100\">\n          <div className=\"text-sm text-gray-500 font-medium\">\n            Trusted & Certified:\n          </div>\n          \n          {certifications.map((cert, index) => {\n            const IconComponent = cert.icon;\n            \n            return (\n              <Badge \n                key={index} \n                variant=\"outline\" \n                className=\"flex items-center gap-2 py-2 px-4 border-gray-200 hover:border-gray-300 transition-colors\"\n              >\n                <IconComponent className={`w-4 h-4 ${cert.color}`} />\n                <span className=\"font-medium\">{cert.name}</span>\n              </Badge>\n            );\n          })}\n        </div>\n\n        {/* Social Proof Quote */}\n        <div className=\"text-center max-w-3xl mx-auto\">\n          <blockquote className=\"text-lg text-gray-600 italic mb-4\">\n            &quot;OffGridFlux provides the most thorough and unbiased portable power station reviews.\n            Their real-world testing helped me choose the perfect setup for my off-grid cabin.&quot;\n          </blockquote>\n          <div className=\"flex items-center justify-center gap-2\">\n            <div className=\"flex text-yellow-400\">\n              {[...Array(5)].map((_, i) => (\n                <Star key={i} className=\"w-4 h-4 fill-current\" />\n              ))}\n            </div>\n            <span className=\"text-sm text-gray-500 ml-2\">\n              - Sarah M., Verified Reader\n            </span>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": "AAAA,wCAAwC;AACxC,mDAAmD;;;;;AAEnD;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;AASA,MAAM,eAA8B;IAClC;QACE,MAAM,oMAAA,CAAA,QAAK;QACX,OAAO;QACP,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,oMAAA,CAAA,QAAK;QACX,OAAO;QACP,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,sMAAA,CAAA,SAAM;QACZ,OAAO;QACP,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,oMAAA,CAAA,QAAK;QACX,OAAO;QACP,OAAO;QACP,aAAa;IACf;CACD;AAED,MAAM,iBAAiB;IACrB;QACE,MAAM;QACN,aAAa;QACb,MAAM,2NAAA,CAAA,cAAW;QACjB,OAAO;IACT;IACA;QACE,MAAM;QACN,aAAa;QACb,MAAM,kMAAA,CAAA,OAAI;QACV,OAAO;IACT;IACA;QACE,MAAM;QACN,aAAa;QACb,MAAM,oMAAA,CAAA,QAAK;QACX,OAAO;IACT;CACD;AAEM,SAAS;IACd,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BAGb,8OAAC;oBAAI,WAAU;8BACZ,aAAa,GAAG,CAAC,CAAC,QAAQ;wBACzB,MAAM,gBAAgB,OAAO,IAAI;wBAEjC,qBACE,8OAAC;4BAAgB,WAAU;;8CACzB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAc,WAAU;;;;;;;;;;;8CAE3B,8OAAC;oCAAI,WAAU;8CACZ,OAAO,KAAK;;;;;;8CAEf,8OAAC;oCAAI,WAAU;8CACZ,OAAO,KAAK;;;;;;gCAEd,OAAO,WAAW,kBACjB,8OAAC;oCAAI,WAAU;8CACZ,OAAO,WAAW;;;;;;;2BAZf;;;;;oBAiBd;;;;;;8BAIF,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCAAoC;;;;;;wBAIlD,eAAe,GAAG,CAAC,CAAC,MAAM;4BACzB,MAAM,gBAAgB,KAAK,IAAI;4BAE/B,qBACE,8OAAC,iIAAA,CAAA,QAAK;gCAEJ,SAAQ;gCACR,WAAU;;kDAEV,8OAAC;wCAAc,WAAW,CAAC,QAAQ,EAAE,KAAK,KAAK,EAAE;;;;;;kDACjD,8OAAC;wCAAK,WAAU;kDAAe,KAAK,IAAI;;;;;;;+BALnC;;;;;wBAQX;;;;;;;8BAIF,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAW,WAAU;sCAAoC;;;;;;sCAI1D,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACZ;2CAAI,MAAM;qCAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC,kMAAA,CAAA,OAAI;4CAAS,WAAU;2CAAb;;;;;;;;;;8CAGf,8OAAC;oCAAK,WAAU;8CAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQzD", "debugId": null}}, {"offset": {"line": 1557, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/offgridflux/offgridflux-website/src/app/page.tsx"], "sourcesContent": ["// OffGridFlux - Homepage\r\n// Main landing page showcasing featured products and categories\r\n\r\nimport { Metadata } from 'next';\r\nimport Link from 'next/link';\r\nimport { getFeaturedProducts, getBestValueProducts } from '@/lib/supabase';\r\nimport { ProductCard } from '@/components/ProductCard';\r\nimport { Hero } from '@/components/Hero';\r\nimport { CategoryGrid } from '@/components/CategoryGrid';\r\nimport { NewsletterSignup } from '@/components/NewsletterSignup';\r\nimport { TrustSignals } from '@/components/TrustSignals';\r\n\r\nexport const metadata: Metadata = {\r\n  title: 'OffGridFlux - Best Portable Power Stations 2025 | Expert Reviews & Testing',\r\n  description: 'Unbiased reviews and real-world testing of portable power stations from Jackery, Goal Zero, and EcoFlow. Find the perfect off-grid power solution for camping, emergencies, and more.',\r\n  keywords: 'portable power station, solar generator, off grid power, jackery, goal zero, ecoflow, battery backup, camping power',\r\n  openGraph: {\r\n    title: 'OffGridFlux - Best Portable Power Stations 2025',\r\n    description: 'Expert reviews and real-world testing of portable power stations. Find your perfect off-grid power solution.',\r\n    type: 'website',\r\n    url: 'https://offgridflux.com',\r\n  }\r\n};\r\n\r\nexport default async function HomePage() {\r\n  // Fetch featured and best value products\r\n  const [featuredResult, bestValueResult] = await Promise.all([\r\n    getFeaturedProducts(6),\r\n    getBestValueProducts(5)\r\n  ]);\r\n\r\n  const featuredProducts = featuredResult.data || [];\r\n  const bestValueProducts = bestValueResult.data || [];\r\n\r\n  return (\r\n    <div className=\"bg-white\">\r\n      {/* Hero Section */}\r\n      <Hero />\r\n\r\n      {/* Trust Signals */}\r\n      <TrustSignals />\r\n\r\n      {/* Featured Products */}\r\n      <section className=\"py-20 bg-neutral-50\">\r\n        <div className=\"max-w-7xl mx-auto px-4\">\r\n          <div className=\"text-center mb-16\">\r\n            <div className=\"inline-flex items-center gap-2 bg-primary-100 text-primary-700 rounded-full px-4 py-2 text-sm font-medium mb-4\">\r\n              <span className=\"w-2 h-2 bg-primary-500 rounded-full\"></span>\r\n              Editor&apos;s Choice\r\n            </div>\r\n            <h2 className=\"text-4xl font-bold text-neutral-900 mb-6\">\r\n              Top Rated Power Stations\r\n            </h2>\r\n            <p className=\"text-xl text-neutral-600 max-w-3xl mx-auto leading-relaxed\">\r\n              Our highest-rated portable power stations based on rigorous real-world testing,\r\n              user feedback, and expert analysis.\r\n            </p>\r\n          </div>\r\n\r\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\r\n            {featuredProducts.map((product) => (\r\n              <ProductCard key={product.id} product={product} />\r\n            ))}\r\n          </div>\r\n\r\n          <div className=\"text-center mt-12\">\r\n            <Link\r\n              href=\"/category/portable-power-stations\"\r\n              className=\"inline-flex items-center gap-2 bg-primary-600 hover:bg-primary-700 text-white font-semibold py-4 px-8 rounded-xl transition-all duration-200 hover:scale-105 shadow-lg hover:shadow-xl\"\r\n            >\r\n              View All Power Stations\r\n              <span className=\"text-primary-200\">→</span>\r\n            </Link>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Best Value Section */}\r\n      {bestValueProducts.length > 0 && (\r\n        <section className=\"py-20 bg-white border-t border-neutral-200\">\r\n          <div className=\"max-w-7xl mx-auto px-4\">\r\n            <div className=\"text-center mb-16\">\r\n              <div className=\"inline-flex items-center gap-2 bg-energy-100 text-energy-700 rounded-full px-4 py-2 text-sm font-medium mb-4\">\r\n                <span className=\"w-2 h-2 bg-energy-500 rounded-full\"></span>\r\n                Best Value\r\n              </div>\r\n              <h2 className=\"text-4xl font-bold text-neutral-900 mb-6\">\r\n                Maximum Capacity Per Dollar\r\n              </h2>\r\n              <p className=\"text-xl text-neutral-600 max-w-3xl mx-auto leading-relaxed\">\r\n                Get the most power capacity for your investment with these exceptional value picks.\r\n              </p>\r\n            </div>\r\n\r\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-6\">\r\n              {bestValueProducts.map((product) => (\r\n                <ProductCard key={product.id} product={product} />\r\n              ))}\r\n            </div>\r\n          </div>\r\n        </section>\r\n      )}\r\n\r\n      {/* Category Grid */}\r\n      <CategoryGrid />\r\n\r\n      {/* Newsletter Signup */}\r\n      <NewsletterSignup />\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA,yBAAyB;AACzB,gEAAgE;;;;;;AAGhE;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;AAEO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;IACb,UAAU;IACV,WAAW;QACT,OAAO;QACP,aAAa;QACb,MAAM;QACN,KAAK;IACP;AACF;AAEe,eAAe;IAC5B,yCAAyC;IACzC,MAAM,CAAC,gBAAgB,gBAAgB,GAAG,MAAM,QAAQ,GAAG,CAAC;QAC1D,CAAA,GAAA,sHAAA,CAAA,sBAAmB,AAAD,EAAE;QACpB,CAAA,GAAA,sHAAA,CAAA,uBAAoB,AAAD,EAAE;KACtB;IAED,MAAM,mBAAmB,eAAe,IAAI,IAAI,EAAE;IAClD,MAAM,oBAAoB,gBAAgB,IAAI,IAAI,EAAE;IAEpD,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,0HAAA,CAAA,OAAI;;;;;0BAGL,8OAAC,kIAAA,CAAA,eAAY;;;;;0BAGb,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;;;;;;wCAA6C;;;;;;;8CAG/D,8OAAC;oCAAG,WAAU;8CAA2C;;;;;;8CAGzD,8OAAC;oCAAE,WAAU;8CAA6D;;;;;;;;;;;;sCAM5E,8OAAC;4BAAI,WAAU;sCACZ,iBAAiB,GAAG,CAAC,CAAC,wBACrB,8OAAC,iIAAA,CAAA,cAAW;oCAAkB,SAAS;mCAArB,QAAQ,EAAE;;;;;;;;;;sCAIhC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;oCACX;kDAEC,8OAAC;wCAAK,WAAU;kDAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAO1C,kBAAkB,MAAM,GAAG,mBAC1B,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;;;;;;wCAA4C;;;;;;;8CAG9D,8OAAC;oCAAG,WAAU;8CAA2C;;;;;;8CAGzD,8OAAC;oCAAE,WAAU;8CAA6D;;;;;;;;;;;;sCAK5E,8OAAC;4BAAI,WAAU;sCACZ,kBAAkB,GAAG,CAAC,CAAC,wBACtB,8OAAC,iIAAA,CAAA,cAAW;oCAAkB,SAAS;mCAArB,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;0BAQtC,8OAAC,kIAAA,CAAA,eAAY;;;;;0BAGb,8OAAC,sIAAA,CAAA,mBAAgB;;;;;;;;;;;AAGvB", "debugId": null}}]}