(()=>{var e={};e.id=220,e.ids=[220],e.modules={84:(e,t,s)=>{"use strict";s.d(t,{E:()=>o});var r=s(7413);s(1120);var a=s(403),i=s(662),n=s(974);let l=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function o({className:e,variant:t,asChild:s=!1,...i}){let o=s?a.DX:"span";return(0,r.jsx)(o,{"data-slot":"badge",className:(0,n.cn)(l({variant:t}),e),...i})}},440:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(1658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},974:(e,t,s)=>{"use strict";s.d(t,{cn:()=>i});var r=s(5986),a=s(8974);function i(...e){return(0,a.QP)((0,r.$)(e))}},1142:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(6373).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},2989:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>u,tree:()=>d});var r=s(5239),a=s(8088),i=s(8170),n=s.n(i),l=s(893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);s.d(t,o);let d={children:["",{children:["about",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,5197)),"C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\app\\about\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,4431)),"C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\app\\about\\page.tsx"],x={require:s,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/about/page",pathname:"/about",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3148:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(6373).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},5197:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>p,metadata:()=>m});var r=s(7413),a=s(8963);function i({name:e,role:t,bio:s}){return(0,r.jsx)(a.Zp,{className:"text-center hover:shadow-lg transition-shadow duration-300",children:(0,r.jsxs)(a.Wu,{className:"p-6",children:[(0,r.jsx)("div",{className:"relative w-24 h-24 mx-auto mb-4",children:(0,r.jsx)("div",{className:"w-24 h-24 bg-gray-200 rounded-full flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-gray-500 text-sm",children:"Photo"})})}),(0,r.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:e}),(0,r.jsx)("p",{className:"text-primary-600 font-medium mb-3",children:t}),(0,r.jsx)("p",{className:"text-gray-600 text-sm leading-relaxed",children:s})]})})}var n=s(84),l=s(1142),o=s(5805),d=s(3148),c=s(9117);let x=[{step:1,title:"Initial Assessment",duration:"2-3 days",description:"Unboxing, build quality inspection, and initial capacity testing",icon:l.A,color:"bg-blue-500"},{step:2,title:"Performance Testing",duration:"1 week",description:"Load testing, efficiency measurements, and charging speed analysis",icon:o.A,color:"bg-yellow-500"},{step:3,title:"Real-World Usage",duration:"2-3 weeks",description:"Camping trips, emergency scenarios, and daily use testing",icon:d.A,color:"bg-green-500"},{step:4,title:"Safety & Durability",duration:"1 week",description:"Temperature testing, drop tests, and safety feature validation",icon:c.A,color:"bg-red-500"}];function u(){return(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:x.map(e=>{let t=e.icon;return(0,r.jsx)(a.Zp,{className:"relative overflow-hidden hover:shadow-lg transition-shadow duration-300",children:(0,r.jsxs)(a.Wu,{className:"p-6",children:[(0,r.jsx)("div",{className:`w-12 h-12 ${e.color} rounded-full flex items-center justify-center text-white font-bold text-lg mb-4`,children:e.step}),(0,r.jsx)("div",{className:"mb-4",children:(0,r.jsx)(t,{className:"w-8 h-8 text-gray-600"})}),(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:e.title}),(0,r.jsx)(n.E,{variant:"outline",className:"mb-3 text-xs",children:e.duration}),(0,r.jsx)("p",{className:"text-gray-600 text-sm leading-relaxed",children:e.description})]})},e.step)})})}let m={title:"About OffGridFlux - Real-World Power Station Testing",description:"Learn about OffGridFlux's mission to provide unbiased, real-world testing of portable power stations and off-grid power solutions.",keywords:"about offgridflux, power station testing, off grid experts, portable power reviews",openGraph:{title:"About OffGridFlux - Real-World Power Station Testing",description:"Learn about OffGridFlux's mission to provide unbiased, real-world testing of portable power stations and off-grid power solutions.",type:"website",url:"https://offgridflux.com/about"}};function p(){return(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,r.jsx)("section",{className:"bg-gradient-to-r from-blue-600 to-blue-800 text-white py-16",children:(0,r.jsxs)("div",{className:"max-w-4xl mx-auto px-4 text-center",children:[(0,r.jsx)("h1",{className:"text-4xl md:text-5xl font-bold mb-6",children:"About OffGridFlux"}),(0,r.jsx)("p",{className:"text-xl md:text-2xl text-blue-100 leading-relaxed",children:"Unplugged Power, Real-World Testing"})]})}),(0,r.jsx)("section",{className:"py-16 bg-white",children:(0,r.jsxs)("div",{className:"max-w-4xl mx-auto px-4",children:[(0,r.jsxs)("div",{className:"text-center mb-12",children:[(0,r.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Our Mission"}),(0,r.jsx)("p",{className:"text-lg text-gray-600 leading-relaxed",children:"At OffGridFlux, we believe everyone deserves reliable, independent power solutions. Our mission is to provide unbiased, real-world testing and reviews of portable power stations, solar generators, and off-grid power equipment to help you make informed decisions."})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,r.jsx)("svg",{className:"w-8 h-8 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,r.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Unbiased Testing"}),(0,r.jsx)("p",{className:"text-gray-600",children:"We purchase and test every product ourselves, ensuring completely independent reviews."})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,r.jsx)("svg",{className:"w-8 h-8 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 10V3L4 14h7v7l9-11h-7z"})})}),(0,r.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Real-World Conditions"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Our testing goes beyond lab conditions to real camping, emergency, and off-grid scenarios."})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,r.jsx)("svg",{className:"w-8 h-8 text-purple-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"})})}),(0,r.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Educational Content"}),(0,r.jsx)("p",{className:"text-gray-600",children:"We provide comprehensive guides to help you understand and choose the right power solutions."})]})]})]})}),(0,r.jsx)("section",{className:"py-16 bg-gray-50",children:(0,r.jsxs)("div",{className:"max-w-6xl mx-auto px-4",children:[(0,r.jsxs)("div",{className:"text-center mb-12",children:[(0,r.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Our Testing Process"}),(0,r.jsx)("p",{className:"text-lg text-gray-600",children:"Every product goes through our rigorous 30-day testing protocol"})]}),(0,r.jsx)(u,{})]})}),(0,r.jsx)("section",{className:"py-16 bg-white",children:(0,r.jsxs)("div",{className:"max-w-6xl mx-auto px-4",children:[(0,r.jsxs)("div",{className:"text-center mb-12",children:[(0,r.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Meet Our Team"}),(0,r.jsx)("p",{className:"text-lg text-gray-600",children:"Experienced off-grid enthusiasts and electrical engineers"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:[(0,r.jsx)(i,{name:"Mike Johnson",role:"Founder & Lead Tester",bio:"15+ years in renewable energy systems and off-grid living. Former electrical engineer turned full-time off-grid advocate.",image:"/team/mike-johnson.jpg"}),(0,r.jsx)(i,{name:"Sarah Chen",role:"Technical Writer",bio:"Mechanical engineer with expertise in battery technology and power systems. Specializes in making complex tech accessible.",image:"/team/sarah-chen.jpg"}),(0,r.jsx)(i,{name:"David Rodriguez",role:"Field Tester",bio:"Professional outdoor guide and camping expert. Tests all equipment in real-world outdoor conditions.",image:"/team/david-rodriguez.jpg"})]})]})}),(0,r.jsx)("section",{className:"py-16 bg-gray-50",children:(0,r.jsxs)("div",{className:"max-w-4xl mx-auto px-4",children:[(0,r.jsx)("div",{className:"text-center mb-12",children:(0,r.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Our Values"})}),(0,r.jsxs)("div",{className:"space-y-8",children:[(0,r.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center flex-shrink-0 mt-1",children:(0,r.jsx)("span",{className:"text-white font-bold",children:"1"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Transparency"}),(0,r.jsx)("p",{className:"text-gray-600",children:"We clearly disclose our testing methods, affiliate relationships, and any potential conflicts of interest."})]})]}),(0,r.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center flex-shrink-0 mt-1",children:(0,r.jsx)("span",{className:"text-white font-bold",children:"2"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Independence"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Our reviews are never influenced by manufacturers. We purchase products at retail price and test them objectively."})]})]}),(0,r.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center flex-shrink-0 mt-1",children:(0,r.jsx)("span",{className:"text-white font-bold",children:"3"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Accuracy"}),(0,r.jsx)("p",{className:"text-gray-600",children:"We use calibrated testing equipment and standardized procedures to ensure accurate, repeatable results."})]})]}),(0,r.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center flex-shrink-0 mt-1",children:(0,r.jsx)("span",{className:"text-white font-bold",children:"4"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Community"}),(0,r.jsx)("p",{className:"text-gray-600",children:"We actively engage with our readers and the off-grid community to understand real-world needs and challenges."})]})]})]})]})}),(0,r.jsx)("section",{className:"py-16 bg-blue-600 text-white",children:(0,r.jsxs)("div",{className:"max-w-4xl mx-auto px-4 text-center",children:[(0,r.jsx)("h2",{className:"text-3xl font-bold mb-6",children:"Get In Touch"}),(0,r.jsx)("p",{className:"text-xl text-blue-100 mb-8",children:"Have questions about our testing or suggestions for products to review?"}),(0,r.jsx)("a",{href:"/contact",className:"inline-block bg-white text-blue-600 font-semibold py-3 px-8 rounded-lg hover:bg-gray-100 transition-colors duration-200",children:"Contact Us"})]})})]})}},5805:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(6373).A)("zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]])},6487:()=>{},8335:()=>{},8963:(e,t,s)=>{"use strict";s.d(t,{Wu:()=>n,Zp:()=>i});var r=s(7413);s(1120);var a=s(974);function i({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t})}function n({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",e),...t})}},9117:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(6373).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[447,455,342,255],()=>s(2989));module.exports=r})();