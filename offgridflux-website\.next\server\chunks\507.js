"use strict";exports.id=507,exports.ids=[507],exports.modules={18898:(e,t,i)=>{i.d(t,{A:()=>s});let s=(0,i(26373).A)("house",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]])},23249:(e,t,i)=>{i.d(t,{NewsletterSignup:()=>u});var s=i(60687),r=i(43210),a=i(29523),l=i(89667),n=i(96834),d=i(45583),c=i(62688);let o=(0,c.A)("gift",[["rect",{x:"3",y:"8",width:"18",height:"4",rx:"1",key:"bkv52"}],["path",{d:"M12 8v13",key:"1c76mn"}],["path",{d:"M19 12v7a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2v-7",key:"6wjy6b"}],["path",{d:"M7.5 8a2.5 2.5 0 0 1 0-5A4.8 8 0 0 1 12 8a4.8 8 0 0 1 4.5-5 2.5 2.5 0 0 1 0 5",key:"1ihvrl"}]]),h=(0,c.A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]]),m=(0,c.A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]),x=(0,c.A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]]),p=[{icon:d.A,title:"Latest Reviews",description:"Get notified when we publish new product reviews"},{icon:o,title:"Exclusive Deals",description:"Access to subscriber-only discounts and promotions"},{icon:h,title:"Buying Guides",description:"In-depth guides to help you make the right choice"}];function u(){let[e,t]=(0,r.useState)(""),[i,d]=(0,r.useState)(!1),[c,o]=(0,r.useState)(!1),h=async i=>{i.preventDefault(),e&&(o(!0),await new Promise(e=>setTimeout(e,1e3)),d(!0),o(!1),t(""))};return i?(0,s.jsx)("section",{className:"py-16 bg-primary-600 text-white",children:(0,s.jsxs)("div",{className:"max-w-4xl mx-auto px-4 text-center",children:[(0,s.jsx)("div",{className:"inline-flex items-center justify-center w-16 h-16 bg-green-500 rounded-full mb-6",children:(0,s.jsx)(m,{className:"w-8 h-8 text-white"})}),(0,s.jsx)("h2",{className:"text-3xl font-bold mb-4",children:"Welcome to the OffGridFlux Community! \uD83C\uDF89"}),(0,s.jsx)("p",{className:"text-xl text-primary-100 mb-6",children:"Check your email for a confirmation link and your welcome guide."}),(0,s.jsx)(a.$,{variant:"outline",className:"border-white text-white hover:bg-white hover:text-primary-600",onClick:()=>d(!1),children:"Subscribe Another Email"})]})}):(0,s.jsx)("section",{className:"py-16 bg-primary-600 text-white",children:(0,s.jsx)("div",{className:"max-w-6xl mx-auto px-4",children:(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)(n.E,{className:"mb-4 bg-white/10 text-white border-white/20",children:[(0,s.jsx)(x,{className:"w-4 h-4 mr-2"}),"Free Newsletter"]}),(0,s.jsx)("h2",{className:"text-3xl md:text-4xl font-bold mb-6",children:"Stay Powered Up with Expert Insights"}),(0,s.jsx)("p",{className:"text-xl text-primary-100 mb-8 leading-relaxed",children:"Join 10,000+ off-grid enthusiasts getting the latest reviews, buying guides, and exclusive deals delivered to their inbox."}),(0,s.jsx)("div",{className:"space-y-4",children:p.map((e,t)=>{let i=e.icon;return(0,s.jsxs)("div",{className:"flex items-start gap-4",children:[(0,s.jsx)("div",{className:"flex-shrink-0 w-10 h-10 bg-white/10 rounded-lg flex items-center justify-center",children:(0,s.jsx)(i,{className:"w-5 h-5 text-white"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"font-semibold text-white mb-1",children:e.title}),(0,s.jsx)("p",{className:"text-primary-100 text-sm",children:e.description})]})]},t)})})]}),(0,s.jsxs)("div",{className:"bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20",children:[(0,s.jsxs)("form",{onSubmit:h,className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-white mb-2",children:"Email Address"}),(0,s.jsx)(l.p,{id:"email",type:"email",value:e,onChange:e=>t(e.target.value),placeholder:"Enter your email address",required:!0,className:"bg-white/20 border-white/30 text-white placeholder:text-white/60 focus:border-white focus:ring-white"})]}),(0,s.jsx)(a.$,{type:"submit",disabled:c||!e,className:"w-full bg-white text-primary-600 hover:bg-gray-100 font-semibold py-3",children:c?(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:"w-4 h-4 border-2 border-primary-600 border-t-transparent rounded-full animate-spin"}),"Subscribing..."]}):"Get Free Updates"}),(0,s.jsxs)("p",{className:"text-xs text-primary-200 text-center",children:["No spam, ever. Unsubscribe anytime with one click.",(0,s.jsx)("br",{}),"By subscribing, you agree to our"," ",(0,s.jsx)("a",{href:"/privacy",className:"underline hover:text-white",children:"Privacy Policy"}),"."]})]}),(0,s.jsx)("div",{className:"mt-6 pt-6 border-t border-white/20",children:(0,s.jsxs)("div",{className:"flex items-center justify-center gap-4 text-sm text-primary-100",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:"w-2 h-2 bg-green-400 rounded-full"}),(0,s.jsx)("span",{children:"10,000+ subscribers"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:"w-2 h-2 bg-yellow-400 rounded-full"}),(0,s.jsx)("span",{children:"4.9★ rating"})]})]})})]})]})})})}},39128:(e,t,i)=>{i.d(t,{A:()=>s});let s=(0,i(26373).A)("sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]])},53148:(e,t,i)=>{i.d(t,{A:()=>s});let s=(0,i(26373).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},68367:(e,t,i)=>{i.d(t,{NewsletterSignup:()=>s});let s=(0,i(12907).registerClientReference)(function(){throw Error("Attempted to call NewsletterSignup() from the server but NewsletterSignup is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\NewsletterSignup.tsx","NewsletterSignup")},89667:(e,t,i)=>{i.d(t,{p:()=>a});var s=i(60687);i(43210);var r=i(4780);function a({className:e,type:t,...i}){return(0,s.jsx)("input",{type:t,"data-slot":"input",className:(0,r.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...i})}},91142:(e,t,i)=>{i.d(t,{A:()=>s});let s=(0,i(26373).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])}};