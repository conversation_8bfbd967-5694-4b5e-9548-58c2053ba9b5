{"kind": "FETCH", "data": {"headers": {"alt-svc": "h3=\":443\"; ma=86400", "cf-cache-status": "DYNAMIC", "cf-ray": "9488b2561b64a1d0-MSP", "connection": "keep-alive", "content-encoding": "gzip", "content-location": "/product_specs?availability_status=eq.In%20Stock&capacity_wh=not.is.null&current_price_usd=not.is.null&select=%2A", "content-profile": "public", "content-range": "0-16/*", "content-type": "application/json; charset=utf-8", "date": "Sat, 31 May 2025 18:57:23 GMT", "sb-gateway-version": "1", "sb-project-ref": "binxxcezkmkwvjbyiatc", "server": "cloudflare", "set-cookie": "__cf_bm=21YmJz6bJPcNtdy3h6V.ujDBSNk2RVJXOCPjPei.rK4-1748717843-*******-xe8R2tn7bSdytLWkfBaLZaCI43..NDzSKtFp3UHovEJE8hKUPIQ.RvZKQFGm0nPSVuzLv7Q5XBPS8hwkTQP_9rz5uSCyzf4_dXv__zsSOrU; path=/; expires=Sat, 31-May-25 19:27:23 GMT; domain=.supabase.co; HttpOnly; Secure; SameSite=None", "strict-transport-security": "max-age=31536000; includeSubDomains; preload", "transfer-encoding": "chunked", "vary": "Accept-Encoding", "x-content-type-options": "nosniff", "x-envoy-attempt-count": "1", "x-envoy-upstream-service-time": "17"}, "body": "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", "status": 200, "url": "https://binxxcezkmkwvjbyiatc.supabase.co/rest/v1/product_specs?select=*&availability_status=eq.In+Stock&current_price_usd=not.is.null&capacity_wh=not.is.null"}, "revalidate": 31536000, "tags": []}