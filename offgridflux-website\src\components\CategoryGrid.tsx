// OffGridFlux - Category Grid Component
// Displays product categories in a grid layout

import Link from 'next/link';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Zap, Sun, Home, ArrowRight } from 'lucide-react';

interface Category {
  slug: string;
  title: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  gradient: string;
  iconBg: string;
  productCount: number;
  priceRange: string;
  featured?: boolean;
}

const categories: Category[] = [
  {
    slug: 'portable-power-stations',
    title: 'Portable Power Stations',
    description: 'Compact power for camping, emergencies, and outdoor adventures',
    icon: Zap,
    gradient: 'from-blue-50 to-blue-100',
    iconBg: 'bg-primary-600',
    productCount: 12,
    priceRange: '$149 - $999',
    featured: true,
  },
  {
    slug: 'solar-generators',
    title: 'Solar Generators',
    description: 'Renewable power solutions with integrated solar panels',
    icon: Sun,
    gradient: 'from-yellow-50 to-yellow-100',
    iconBg: 'bg-warning-600',
    productCount: 8,
    priceRange: '$299 - $1,499',
  },
  {
    slug: 'home-battery-systems',
    title: 'Home Battery Systems',
    description: 'Whole home backup power and grid independence solutions',
    icon: Home,
    gradient: 'from-green-50 to-green-100',
    iconBg: 'bg-energy-600',
    productCount: 5,
    priceRange: '$2,999 - $5,799',
  },
];

export function CategoryGrid() {
  return (
    <section className="py-16 bg-white">
      <div className="max-w-6xl mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            Shop by Category
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Find the perfect power solution for your specific needs and budget
          </p>
        </div>

        {/* Categories Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {categories.map((category) => {
            const IconComponent = category.icon;
            
            return (
              <Link 
                key={category.slug} 
                href={`/category/${category.slug}`}
                className="group block"
              >
                <Card className="h-full hover:shadow-lg transition-all duration-300 group-hover:scale-105 border-0 shadow-soft">
                  <CardContent className={`p-0 bg-gradient-to-br ${category.gradient} rounded-lg overflow-hidden`}>
                    <div className="p-8 relative">
                      {/* Featured Badge */}
                      {category.featured && (
                        <Badge className="absolute top-4 right-4 bg-primary-600 text-white">
                          Most Popular
                        </Badge>
                      )}
                      
                      {/* Icon */}
                      <div className={`w-16 h-16 ${category.iconBg} rounded-full flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300`}>
                        <IconComponent className="w-8 h-8 text-white" />
                      </div>
                      
                      {/* Content */}
                      <h3 className="text-xl font-semibold text-gray-900 mb-3 group-hover:text-primary-600 transition-colors">
                        {category.title}
                      </h3>
                      
                      <p className="text-gray-600 mb-4 leading-relaxed">
                        {category.description}
                      </p>
                      
                      {/* Stats */}
                      <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                        <span>{category.productCount} products</span>
                        <span>{category.priceRange}</span>
                      </div>
                      
                      {/* CTA */}
                      <div className="flex items-center text-primary-600 font-medium group-hover:text-primary-700 transition-colors">
                        <span>Explore Category</span>
                        <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </Link>
            );
          })}
        </div>

        {/* Bottom CTA */}
        <div className="text-center mt-12">
          <div className="bg-gray-50 rounded-lg p-8">
            <h3 className="text-xl font-semibold text-gray-900 mb-4">
              Not sure which category is right for you?
            </h3>
            <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
              Our buying guide helps you choose the perfect power solution based on your specific needs,
              budget, and use cases.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link 
                href="/guides/buying-guide"
                className="inline-flex items-center justify-center bg-primary-600 hover:bg-primary-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-200"
              >
                View Buying Guide
              </Link>
              <Link 
                href="/comparison"
                className="inline-flex items-center justify-center border border-gray-300 hover:border-gray-400 text-gray-700 font-semibold py-3 px-6 rounded-lg transition-colors duration-200"
              >
                Compare All Products
              </Link>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
