(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[169],{7308:(e,s,r)=>{"use strict";r.d(s,{AffiliateDisclosure:()=>m});var a=r(5155),i=r(2115),l=r(6126),n=r(285),t=r(9946);let o=(0,t.A)("info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]]),c=(0,t.A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]]),d=(0,t.A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]);function m(){let[e,s]=(0,i.useState)(!0),[r,t]=(0,i.useState)(!1);return e?(0,a.jsx)("div",{className:"bg-primary-50 border-b border-primary-200 relative",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 py-3",children:(0,a.jsxs)("div",{className:"flex items-start gap-3",children:[(0,a.jsx)("div",{className:"flex-shrink-0 mt-0.5",children:(0,a.jsx)(o,{className:"w-5 h-5 text-primary-600"})}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsx)("div",{className:"flex items-center gap-2 mb-1",children:(0,a.jsx)(l.E,{variant:"outline",className:"text-xs border-primary-300 text-primary-700",children:"FTC Disclosure"})}),(0,a.jsxs)("div",{className:"text-sm text-primary-800",children:[(0,a.jsx)("p",{className:"font-medium mb-1",children:"Affiliate Disclosure: We earn commissions from qualifying purchases."}),r?(0,a.jsxs)("div",{className:"text-primary-700 space-y-2",children:[(0,a.jsx)("p",{children:"OffGridFlux participates in affiliate programs with Amazon, Jackery, Goal Zero, EcoFlow, and other retailers. When you click on links to products and make a purchase, we may receive a commission at no additional cost to you."}),(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Our Promise:"})," We only recommend products we've personally tested or would use ourselves. Our reviews are based on real-world testing and are not influenced by affiliate relationships. We purchase most products at full retail price for testing."]}),(0,a.jsxs)("p",{children:["Commission earnings help support our independent testing lab and keep our content free for readers. For complete details, see our"," ",(0,a.jsxs)("a",{href:"/affiliate-disclosure",className:"underline hover:no-underline font-medium inline-flex items-center gap-1",children:["full affiliate disclosure policy",(0,a.jsx)(c,{className:"w-3 h-3"})]}),"."]}),(0,a.jsx)("button",{onClick:()=>t(!1),className:"underline hover:no-underline font-medium text-xs",children:"Show less"})]}):(0,a.jsxs)("p",{className:"text-primary-700",children:["As an Amazon Associate and affiliate partner, we earn from qualifying purchases. This doesn't affect our review process or recommendations."," ",(0,a.jsx)("button",{onClick:()=>t(!0),className:"underline hover:no-underline font-medium",children:"Read full disclosure"})]})]})]}),(0,a.jsx)(n.$,{variant:"ghost",size:"sm",onClick:()=>s(!1),className:"flex-shrink-0 h-8 w-8 p-0 text-primary-600 hover:bg-primary-100","aria-label":"Close disclosure",children:(0,a.jsx)(d,{className:"w-4 h-4"})})]})})}):null}},8294:(e,s,r)=>{Promise.resolve().then(r.t.bind(r,6874,23)),Promise.resolve().then(r.bind(r,7308)),Promise.resolve().then(r.bind(r,1779))}},e=>{var s=s=>e(e.s=s);e.O(0,[497,800,779,441,684,358],()=>s(8294)),_N_E=e.O()}]);