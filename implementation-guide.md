# OffGridFlux Visual QA Implementation Guide

## 🎯 Priority 1: Critical Color Consistency Fixes

### Step 1: Apply Color Palette Corrections

**Immediate Action Required** - These hardcoded colors violate brand consistency:

```bash
# Apply the patch file
cd offgridflux-website
git apply ../fix-color-contrast.patch
```

### Step 2: Verify Brand Color Usage

Update `tailwind.config.js` to ensure all brand colors are properly defined:

```javascript
// Verify these colors are in your config
colors: {
  primary: { /* blue scale */ },
  energy: { /* green scale for eco/energy */ },
  warning: { /* amber/orange for alerts */ },
  neutral: { /* gray scale with blue tint */ }
}
```

### Step 3: Audit Remaining Components

Run this command to find any remaining hardcoded colors:

```bash
# Search for hardcoded hex values
grep -r "#[0-9A-Fa-f]\{6\}" src/components/
grep -r "rgb(" src/components/
grep -r "rgba(" src/components/
```

## 🔧 Priority 2: Accessibility Compliance (WCAG 2.1 AA)

### Step 1: Install Accessibility Testing Tools

```bash
cd offgridflux-website
npm install --save-dev @axe-core/puppeteer axe-core
```

### Step 2: Run Automated Accessibility Audit

```bash
# Copy the audit script to your project
cp ../accessibility-audit-script.js ./scripts/
node scripts/accessibility-audit-script.js
```

### Step 3: Fix Critical ARIA Issues

**ProductCard.tsx** - Add missing ARIA labels:

```tsx
// Before
<a href={product.affiliate_url} target="_blank" rel="noopener noreferrer">
  Check Price
</a>

// After  
<a 
  href={product.affiliate_url} 
  target="_blank" 
  rel="noopener noreferrer"
  aria-label={`Check price for ${product.product_name} on retailer site`}
>
  Check Price
</a>
```

### Step 4: Verify Color Contrast Ratios

Use browser dev tools or online tools to verify:
- Normal text: minimum 4.5:1 contrast ratio
- Large text (18pt+ bold, 24pt+ regular): minimum 3:1 contrast ratio

**Tools:**
- [WebAIM Contrast Checker](https://webaim.org/resources/contrastchecker/)
- Chrome DevTools Accessibility panel
- [Colour Contrast Analyser](https://www.tpgi.com/color-contrast-checker/)

## 📱 Priority 3: Responsive Design Validation

### Step 1: Test Key Breakpoints

Test these specific viewport sizes:
- **Mobile**: 375px, 414px
- **Tablet**: 768px, 834px  
- **Desktop**: 1024px, 1280px, 1440px

### Step 2: Verify Grid Behavior

Ensure these patterns work correctly:
```tsx
// Category grid should collapse properly
<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">

// Product cards should stack on mobile
<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
```

## 🧪 Testing & Validation Checklist

### Manual Testing (Required)

- [ ] **Color Contrast**: Use browser dev tools to verify all text meets WCAG AA
- [ ] **Keyboard Navigation**: Tab through entire site, verify focus indicators
- [ ] **Screen Reader**: Test with NVDA (free) or VoiceOver (Mac)
- [ ] **Mobile Touch**: Verify touch targets are minimum 44px × 44px
- [ ] **Responsive**: Test all breakpoints for layout integrity

### Automated Testing (Recommended)

```bash
# Lint check
npm run lint

# Accessibility audit
node scripts/accessibility-audit-script.js

# Bundle size check
npm run build
ls -lh .next/static/css/

# Lighthouse audit
npx lighthouse http://localhost:3000 --only-categories=accessibility
```

## 🚀 Deployment Checklist

### Pre-Deployment

- [ ] All color palette fixes applied and tested
- [ ] ARIA labels added to interactive elements
- [ ] Contrast ratios verified (4.5:1 minimum)
- [ ] Keyboard navigation tested
- [ ] Responsive breakpoints validated
- [ ] Bundle size under 200KB (gzipped CSS)

### Post-Deployment

- [ ] Run full accessibility audit on production
- [ ] Verify font loading performance
- [ ] Test on real devices (iOS Safari, Android Chrome)
- [ ] Monitor Core Web Vitals
- [ ] Schedule regular accessibility audits

## 📚 Resources & References

### WCAG 2.1 AA Guidelines
- [WebAIM WCAG Checklist](https://webaim.org/standards/wcag/checklist)
- [W3C ARIA Authoring Practices](https://www.w3.org/WAI/ARIA/apg/)
- [ADA.gov Web Accessibility](https://www.ada.gov/resources/web-guidance/)

### Tailwind CSS Best Practices
- [Tailwind Colors Documentation](https://tailwindcss.com/docs/colors)
- [Responsive Design Guide](https://tailwindcss.com/docs/responsive-design)
- [Accessibility Features](https://tailwindcss.com/docs/screen-readers)

### Testing Tools
- [axe-core](https://github.com/dequelabs/axe-core) - Automated accessibility testing
- [Lighthouse](https://developers.google.com/web/tools/lighthouse) - Performance & accessibility audits
- [WAVE](https://wave.webaim.org/) - Web accessibility evaluation

## 🔄 Ongoing Maintenance

### Weekly Tasks
- [ ] Review new components for color consistency
- [ ] Run accessibility audit on new pages
- [ ] Monitor bundle size growth

### Monthly Tasks  
- [ ] Full site accessibility audit
- [ ] Update design system documentation
- [ ] Review and update this implementation guide

### Quarterly Tasks
- [ ] Comprehensive WCAG compliance review
- [ ] User testing with assistive technologies
- [ ] Performance optimization review

---

**Next Review Date**: [Set 1 week from implementation]
**Responsible Team**: Frontend Development + UX/Accessibility
**Escalation Contact**: Technical Lead
