{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/offgridflux/offgridflux-website/src/components/ProductCard.tsx"], "sourcesContent": ["// OffGridFlux - Product Card Component\n// Displays product information in a card format for category and homepage listings\n\n'use client';\n\nimport Link from 'next/link';\nimport Image from 'next/image';\nimport { useEffect } from 'react';\nimport { Product } from '@/lib/supabase';\nimport { useAffiliateTracking, useProductTracking } from '@/components/GoogleAnalytics';\nimport { Battery, Zap, Weight, Star } from 'lucide-react';\n\ninterface ProductCardProps {\n  product: Product;\n}\n\nexport function ProductCard({ product }: ProductCardProps) {\n  const { trackClick } = useAffiliateTracking();\n  const { trackView } = useProductTracking();\n\n  const valueScore = product.current_price_usd ?\n    (product.capacity_wh / parseFloat(String(product.current_price_usd))).toFixed(2) : null;\n\n  // Track product view on mount\n  useEffect(() => {\n    trackView(\n      product.product_name,\n      product.slug,\n      product.brand,\n      product.current_price_usd || 0,\n      'Portable Power Station'\n    );\n  }, [product, trackView]);\n\n  const handleAffiliateClick = () => {\n    trackClick(\n      product.product_name,\n      product.slug,\n      product.brand,\n      product.current_price_usd || 0,\n      product.affiliate_url || ''\n    );\n  };\n\n  return (\n    <div className=\"group bg-white rounded-xl shadow-soft hover:shadow-large transition-all duration-300 overflow-hidden border border-neutral-100 hover:border-primary-200 hover:-translate-y-1\">\n      {/* Product Image */}\n      <div className=\"relative h-52 bg-gradient-to-br from-neutral-50 to-neutral-100\">\n        {product.product_image_url ? (\n          <Image\n            src={product.product_image_url}\n            alt={product.product_name}\n            fill\n            className=\"object-contain p-6 group-hover:scale-105 transition-transform duration-300\"\n          />\n        ) : (\n          <div className=\"flex items-center justify-center h-full\">\n            <Battery className=\"w-16 h-16 text-neutral-400\" />\n          </div>\n        )}\n\n        {/* Discount Badge */}\n        {product.discount_percent && (\n          <div className=\"absolute top-3 right-3 bg-gradient-to-r from-warning-500 to-warning-600 text-white text-sm font-bold px-3 py-1.5 rounded-full shadow-lg\">\n            {product.discount_percent}% OFF\n          </div>\n        )}\n\n        {/* Brand Badge */}\n        <div className=\"absolute top-3 left-3 bg-white/95 backdrop-blur-sm text-neutral-700 text-sm font-semibold px-3 py-1.5 rounded-full border border-neutral-200 shadow-sm\">\n          {product.brand}\n        </div>\n\n        {/* Quick View Overlay */}\n        <div className=\"absolute inset-0 bg-black/0 group-hover:bg-black/5 transition-colors duration-300\"></div>\n      </div>\n\n      {/* Product Info */}\n      <div className=\"p-6\">\n        {/* Product Name */}\n        <h3 className=\"font-bold text-neutral-900 mb-3 text-lg leading-tight\">\n          <Link\n            href={`/product/${product.slug}`}\n            className=\"hover:text-primary-600 transition-colors group-hover:text-primary-600\"\n          >\n            {product.product_name}\n          </Link>\n        </h3>\n\n        {/* Key Specs */}\n        <div className=\"grid grid-cols-2 gap-3 mb-4\">\n          <div className=\"flex items-center gap-2 text-neutral-600 bg-neutral-50 rounded-lg px-3 py-2\">\n            <Battery className=\"w-4 h-4 text-primary-500\" />\n            <span className=\"font-medium text-sm\">{product.capacity_wh}Wh</span>\n          </div>\n          <div className=\"flex items-center gap-2 text-neutral-600 bg-neutral-50 rounded-lg px-3 py-2\">\n            <Zap className=\"w-4 h-4 text-warning-500\" />\n            <span className=\"font-medium text-sm\">{product.max_output_w}W</span>\n          </div>\n          {product.weight_lbs && (\n            <div className=\"flex items-center gap-2 text-neutral-600 bg-neutral-50 rounded-lg px-3 py-2\">\n              <Weight className=\"w-4 h-4 text-energy-500\" />\n              <span className=\"font-medium text-sm\">{product.weight_lbs}lbs</span>\n            </div>\n          )}\n          {product.battery_type && (\n            <div className=\"bg-neutral-50 rounded-lg px-3 py-2\">\n              <span className=\"text-neutral-600 font-medium text-xs\">{product.battery_type}</span>\n            </div>\n          )}\n        </div>\n\n        {/* Rating */}\n        {product.offgridflux_rating && (\n          <div className=\"flex items-center mb-3\">\n            <div className=\"flex items-center\">\n              {[...Array(5)].map((_, i) => (\n                <Star\n                  key={i}\n                  className={`w-4 h-4 ${\n                    i < Math.floor(product.offgridflux_rating!)\n                      ? 'text-warning-400 fill-current'\n                      : 'text-neutral-300'\n                  }`}\n                />\n              ))}\n            </div>\n            <span className=\"ml-2 text-sm text-gray-600\">\n              {product.offgridflux_rating}/10\n            </span>\n          </div>\n        )}\n\n        {/* Key Features */}\n        {product.key_features && product.key_features.length > 0 && (\n          <div className=\"mb-3\">\n            <ul className=\"text-sm text-gray-600 space-y-1\">\n              {product.key_features.slice(0, 2).map((feature, index) => (\n                <li key={index} className=\"flex items-center\">\n                  <span className=\"w-1.5 h-1.5 bg-green-500 rounded-full mr-2 flex-shrink-0\"></span>\n                  {feature}\n                </li>\n              ))}\n            </ul>\n          </div>\n        )}\n\n        {/* Price and CTA */}\n        <div className=\"border-t border-neutral-100 pt-4 mt-4\">\n          <div className=\"flex items-center justify-between mb-4\">\n            <div>\n              <div className=\"text-2xl font-bold text-neutral-900\">\n                ${product.current_price_usd}\n              </div>\n              {product.msrp_usd && product.msrp_usd !== product.current_price_usd && (\n                <div className=\"text-sm text-neutral-500 line-through\">\n                  ${product.msrp_usd}\n                </div>\n              )}\n            </div>\n            {valueScore && (\n              <div className=\"text-right bg-energy-50 rounded-lg px-3 py-2\">\n                <div className=\"text-xs text-energy-600 font-medium\">Value Score</div>\n                <div className=\"text-lg font-bold text-energy-700\">\n                  {valueScore}\n                </div>\n                <div className=\"text-xs text-energy-600\">Wh/$</div>\n              </div>\n            )}\n          </div>\n\n          <div className=\"space-y-3\">\n            <Link\n              href={`/product/${product.slug}`}\n              className=\"block w-full bg-gradient-to-r from-primary-600 to-primary-700 hover:from-primary-700 hover:to-primary-800 text-white text-center py-3 px-4 rounded-xl transition-all duration-200 font-semibold shadow-lg hover:shadow-xl hover:scale-105\"\n            >\n              View Full Review\n            </Link>\n\n            {product.affiliate_url && (\n              <a\n                href={product.affiliate_url}\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                onClick={handleAffiliateClick}\n                aria-label={`Check price for ${product.product_name} on retailer site`}\n                className=\"block w-full bg-neutral-100 hover:bg-neutral-200 text-neutral-700 text-center py-3 px-4 rounded-xl transition-all duration-200 font-medium border border-neutral-200 hover:border-neutral-300\"\n              >\n                Check Current Price\n              </a>\n            )}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n\n// Skeleton loader for loading states\nexport function ProductCardSkeleton() {\n  return (\n    <div className=\"bg-white rounded-lg shadow-soft overflow-hidden animate-pulse\">\n      <div className=\"h-48 bg-gray-200\"></div>\n      <div className=\"p-4\">\n        <div className=\"h-4 bg-gray-200 rounded mb-2\"></div>\n        <div className=\"h-4 bg-gray-200 rounded w-3/4 mb-3\"></div>\n        <div className=\"grid grid-cols-2 gap-2 mb-3\">\n          <div className=\"h-3 bg-gray-200 rounded\"></div>\n          <div className=\"h-3 bg-gray-200 rounded\"></div>\n        </div>\n        <div className=\"h-3 bg-gray-200 rounded mb-3\"></div>\n        <div className=\"border-t pt-3\">\n          <div className=\"h-6 bg-gray-200 rounded mb-2\"></div>\n          <div className=\"h-8 bg-gray-200 rounded\"></div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,mFAAmF;;;;;;AAInF;AACA;AACA;AAEA;AACA;AAAA;AAAA;AAAA;AAPA;;;;;;;AAaO,SAAS,YAAY,EAAE,OAAO,EAAoB;IACvD,MAAM,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,uBAAoB,AAAD;IAC1C,MAAM,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,qBAAkB,AAAD;IAEvC,MAAM,aAAa,QAAQ,iBAAiB,GAC1C,CAAC,QAAQ,WAAW,GAAG,WAAW,OAAO,QAAQ,iBAAiB,EAAE,EAAE,OAAO,CAAC,KAAK;IAErF,8BAA8B;IAC9B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,UACE,QAAQ,YAAY,EACpB,QAAQ,IAAI,EACZ,QAAQ,KAAK,EACb,QAAQ,iBAAiB,IAAI,GAC7B;IAEJ,GAAG;QAAC;QAAS;KAAU;IAEvB,MAAM,uBAAuB;QAC3B,WACE,QAAQ,YAAY,EACpB,QAAQ,IAAI,EACZ,QAAQ,KAAK,EACb,QAAQ,iBAAiB,IAAI,GAC7B,QAAQ,aAAa,IAAI;IAE7B;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;oBACZ,QAAQ,iBAAiB,iBACxB,8OAAC,6HAAA,CAAA,UAAK;wBACJ,KAAK,QAAQ,iBAAiB;wBAC9B,KAAK,QAAQ,YAAY;wBACzB,IAAI;wBACJ,WAAU;;;;;6CAGZ,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,wMAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;;;;;;oBAKtB,QAAQ,gBAAgB,kBACvB,8OAAC;wBAAI,WAAU;;4BACZ,QAAQ,gBAAgB;4BAAC;;;;;;;kCAK9B,8OAAC;wBAAI,WAAU;kCACZ,QAAQ,KAAK;;;;;;kCAIhB,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAIjB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAG,WAAU;kCACZ,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAM,CAAC,SAAS,EAAE,QAAQ,IAAI,EAAE;4BAChC,WAAU;sCAET,QAAQ,YAAY;;;;;;;;;;;kCAKzB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,wMAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;kDACnB,8OAAC;wCAAK,WAAU;;4CAAuB,QAAQ,WAAW;4CAAC;;;;;;;;;;;;;0CAE7D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,gMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;kDACf,8OAAC;wCAAK,WAAU;;4CAAuB,QAAQ,YAAY;4CAAC;;;;;;;;;;;;;4BAE7D,QAAQ,UAAU,kBACjB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,8OAAC;wCAAK,WAAU;;4CAAuB,QAAQ,UAAU;4CAAC;;;;;;;;;;;;;4BAG7D,QAAQ,YAAY,kBACnB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAK,WAAU;8CAAwC,QAAQ,YAAY;;;;;;;;;;;;;;;;;oBAMjF,QAAQ,kBAAkB,kBACzB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACZ;uCAAI,MAAM;iCAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC,kMAAA,CAAA,OAAI;wCAEH,WAAW,CAAC,QAAQ,EAClB,IAAI,KAAK,KAAK,CAAC,QAAQ,kBAAkB,IACrC,kCACA,oBACJ;uCALG;;;;;;;;;;0CASX,8OAAC;gCAAK,WAAU;;oCACb,QAAQ,kBAAkB;oCAAC;;;;;;;;;;;;;oBAMjC,QAAQ,YAAY,IAAI,QAAQ,YAAY,CAAC,MAAM,GAAG,mBACrD,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAG,WAAU;sCACX,QAAQ,YAAY,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,SAAS,sBAC9C,8OAAC;oCAAe,WAAU;;sDACxB,8OAAC;4CAAK,WAAU;;;;;;wCACf;;mCAFM;;;;;;;;;;;;;;;kCAUjB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAI,WAAU;;oDAAsC;oDACjD,QAAQ,iBAAiB;;;;;;;4CAE5B,QAAQ,QAAQ,IAAI,QAAQ,QAAQ,KAAK,QAAQ,iBAAiB,kBACjE,8OAAC;gDAAI,WAAU;;oDAAwC;oDACnD,QAAQ,QAAQ;;;;;;;;;;;;;oCAIvB,4BACC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAAsC;;;;;;0DACrD,8OAAC;gDAAI,WAAU;0DACZ;;;;;;0DAEH,8OAAC;gDAAI,WAAU;0DAA0B;;;;;;;;;;;;;;;;;;0CAK/C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAM,CAAC,SAAS,EAAE,QAAQ,IAAI,EAAE;wCAChC,WAAU;kDACX;;;;;;oCAIA,QAAQ,aAAa,kBACpB,8OAAC;wCACC,MAAM,QAAQ,aAAa;wCAC3B,QAAO;wCACP,KAAI;wCACJ,SAAS;wCACT,cAAY,CAAC,gBAAgB,EAAE,QAAQ,YAAY,CAAC,iBAAiB,CAAC;wCACtE,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;AAGO,SAAS;IACd,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;;;;;0BACf,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;;;;;;;kCAEjB,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKzB", "debugId": null}}, {"offset": {"line": 520, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/offgridflux/offgridflux-website/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 546, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/offgridflux/offgridflux-website/src/components/NewsletterSignup.tsx"], "sourcesContent": ["// OffGridFlux - Newsletter Signup Component\n// Email subscription form with benefits\n\n'use client';\n\nimport { useState } from 'react';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Badge } from '@/components/ui/badge';\nimport { Mail, CheckCircle, Gift, Zap, FileText } from 'lucide-react';\n\nconst benefits = [\n  {\n    icon: Zap,\n    title: 'Latest Reviews',\n    description: 'Get notified when we publish new product reviews'\n  },\n  {\n    icon: Gift,\n    title: 'Exclusive Deals',\n    description: 'Access to subscriber-only discounts and promotions'\n  },\n  {\n    icon: FileText,\n    title: 'Buying Guides',\n    description: 'In-depth guides to help you make the right choice'\n  }\n];\n\nexport function NewsletterSignup() {\n  const [email, setEmail] = useState('');\n  const [isSubmitted, setIsSubmitted] = useState(false);\n  const [isLoading, setIsLoading] = useState(false);\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    if (!email) return;\n\n    setIsLoading(true);\n    \n    // Simulate API call\n    await new Promise(resolve => setTimeout(resolve, 1000));\n    \n    setIsSubmitted(true);\n    setIsLoading(false);\n    setEmail('');\n  };\n\n  if (isSubmitted) {\n    return (\n      <section className=\"py-16 bg-primary-600 text-white\">\n        <div className=\"max-w-4xl mx-auto px-4 text-center\">\n          <div className=\"inline-flex items-center justify-center w-16 h-16 bg-green-500 rounded-full mb-6\">\n            <CheckCircle className=\"w-8 h-8 text-white\" />\n          </div>\n          <h2 className=\"text-3xl font-bold mb-4\">\n            Welcome to the OffGridFlux Community! 🎉\n          </h2>\n          <p className=\"text-xl text-primary-100 mb-6\">\n            Check your email for a confirmation link and your welcome guide.\n          </p>\n          <Button \n            variant=\"outline\" \n            className=\"border-white text-white hover:bg-white hover:text-primary-600\"\n            onClick={() => setIsSubmitted(false)}\n          >\n            Subscribe Another Email\n          </Button>\n        </div>\n      </section>\n    );\n  }\n\n  return (\n    <section className=\"py-16 bg-primary-600 text-white\">\n      <div className=\"max-w-6xl mx-auto px-4\">\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\">\n          \n          {/* Left Column - Content */}\n          <div>\n            <Badge className=\"mb-4 bg-white/10 text-white border-white/20\">\n              <Mail className=\"w-4 h-4 mr-2\" />\n              Free Newsletter\n            </Badge>\n            \n            <h2 className=\"text-3xl md:text-4xl font-bold mb-6\">\n              Stay Powered Up with Expert Insights\n            </h2>\n            \n            <p className=\"text-xl text-primary-100 mb-8 leading-relaxed\">\n              Join 10,000+ off-grid enthusiasts getting the latest reviews, buying guides, \n              and exclusive deals delivered to their inbox.\n            </p>\n\n            {/* Benefits List */}\n            <div className=\"space-y-4\">\n              {benefits.map((benefit, index) => {\n                const IconComponent = benefit.icon;\n                \n                return (\n                  <div key={index} className=\"flex items-start gap-4\">\n                    <div className=\"flex-shrink-0 w-10 h-10 bg-white/10 rounded-lg flex items-center justify-center\">\n                      <IconComponent className=\"w-5 h-5 text-white\" />\n                    </div>\n                    <div>\n                      <h3 className=\"font-semibold text-white mb-1\">\n                        {benefit.title}\n                      </h3>\n                      <p className=\"text-primary-100 text-sm\">\n                        {benefit.description}\n                      </p>\n                    </div>\n                  </div>\n                );\n              })}\n            </div>\n          </div>\n\n          {/* Right Column - Form */}\n          <div className=\"bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20\">\n            <form onSubmit={handleSubmit} className=\"space-y-6\">\n              <div>\n                <label htmlFor=\"email\" className=\"block text-sm font-medium text-white mb-2\">\n                  Email Address\n                </label>\n                <Input\n                  id=\"email\"\n                  type=\"email\"\n                  value={email}\n                  onChange={(e) => setEmail(e.target.value)}\n                  placeholder=\"Enter your email address\"\n                  required\n                  className=\"bg-white/20 border-white/30 text-white placeholder:text-white/60 focus:border-white focus:ring-white\"\n                />\n              </div>\n              \n              <Button \n                type=\"submit\" \n                disabled={isLoading || !email}\n                className=\"w-full bg-white text-primary-600 hover:bg-gray-100 font-semibold py-3\"\n              >\n                {isLoading ? (\n                  <div className=\"flex items-center gap-2\">\n                    <div className=\"w-4 h-4 border-2 border-primary-600 border-t-transparent rounded-full animate-spin\"></div>\n                    Subscribing...\n                  </div>\n                ) : (\n                  'Get Free Updates'\n                )}\n              </Button>\n              \n              <p className=\"text-xs text-primary-200 text-center\">\n                No spam, ever. Unsubscribe anytime with one click.\n                <br />\n                By subscribing, you agree to our{' '}\n                <a href=\"/privacy\" className=\"underline hover:text-white\">\n                  Privacy Policy\n                </a>\n                .\n              </p>\n            </form>\n\n            {/* Social Proof */}\n            <div className=\"mt-6 pt-6 border-t border-white/20\">\n              <div className=\"flex items-center justify-center gap-4 text-sm text-primary-100\">\n                <div className=\"flex items-center gap-2\">\n                  <div className=\"w-2 h-2 bg-green-400 rounded-full\"></div>\n                  <span>10,000+ subscribers</span>\n                </div>\n                <div className=\"flex items-center gap-2\">\n                  <div className=\"w-2 h-2 bg-yellow-400 rounded-full\"></div>\n                  <span>4.9★ rating</span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": "AAAA,4CAA4C;AAC5C,wCAAwC;;;;;AAIxC;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AANA;;;;;;;AAQA,MAAM,WAAW;IACf;QACE,MAAM,gMAAA,CAAA,MAAG;QACT,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,kMAAA,CAAA,OAAI;QACV,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,8MAAA,CAAA,WAAQ;QACd,OAAO;QACP,aAAa;IACf;CACD;AAEM,SAAS;IACd,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,IAAI,CAAC,OAAO;QAEZ,aAAa;QAEb,oBAAoB;QACpB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QAEjD,eAAe;QACf,aAAa;QACb,SAAS;IACX;IAEA,IAAI,aAAa;QACf,qBACE,8OAAC;YAAQ,WAAU;sBACjB,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,2NAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;kCAEzB,8OAAC;wBAAG,WAAU;kCAA0B;;;;;;kCAGxC,8OAAC;wBAAE,WAAU;kCAAgC;;;;;;kCAG7C,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,WAAU;wBACV,SAAS,IAAM,eAAe;kCAC/B;;;;;;;;;;;;;;;;;IAMT;IAEA,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAGb,8OAAC;;0CACC,8OAAC,iIAAA,CAAA,QAAK;gCAAC,WAAU;;kDACf,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAInC,8OAAC;gCAAG,WAAU;0CAAsC;;;;;;0CAIpD,8OAAC;gCAAE,WAAU;0CAAgD;;;;;;0CAM7D,8OAAC;gCAAI,WAAU;0CACZ,SAAS,GAAG,CAAC,CAAC,SAAS;oCACtB,MAAM,gBAAgB,QAAQ,IAAI;oCAElC,qBACE,8OAAC;wCAAgB,WAAU;;0DACzB,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAc,WAAU;;;;;;;;;;;0DAE3B,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEACX,QAAQ,KAAK;;;;;;kEAEhB,8OAAC;wDAAE,WAAU;kEACV,QAAQ,WAAW;;;;;;;;;;;;;uCAThB;;;;;gCAcd;;;;;;;;;;;;kCAKJ,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAK,UAAU;gCAAc,WAAU;;kDACtC,8OAAC;;0DACC,8OAAC;gDAAM,SAAQ;gDAAQ,WAAU;0DAA4C;;;;;;0DAG7E,8OAAC,iIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,MAAK;gDACL,OAAO;gDACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;gDACxC,aAAY;gDACZ,QAAQ;gDACR,WAAU;;;;;;;;;;;;kDAId,8OAAC,kIAAA,CAAA,SAAM;wCACL,MAAK;wCACL,UAAU,aAAa,CAAC;wCACxB,WAAU;kDAET,0BACC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;;;;;gDAA2F;;;;;;mDAI5G;;;;;;kDAIJ,8OAAC;wCAAE,WAAU;;4CAAuC;0DAElD,8OAAC;;;;;4CAAK;4CAC2B;0DACjC,8OAAC;gDAAE,MAAK;gDAAW,WAAU;0DAA6B;;;;;;4CAEtD;;;;;;;;;;;;;0CAMR,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;8DAAK;;;;;;;;;;;;sDAER,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;8DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASxB", "debugId": null}}]}