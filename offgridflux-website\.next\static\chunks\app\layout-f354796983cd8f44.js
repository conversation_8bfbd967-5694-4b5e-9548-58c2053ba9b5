(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{285:(e,t,r)=>{"use strict";r.d(t,{$:()=>c});var a=r(5155);r(2115);var i=r(4624),n=r(2085),s=r(9434);let o=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function c(e){let{className:t,variant:r,size:n,asChild:c=!1,...l}=e,d=c?i.DX:"button";return(0,a.jsx)(d,{"data-slot":"button",className:(0,s.cn)(o({variant:r,size:n,className:t})),...l})}},347:()=>{},395:(e,t,r)=>{"use strict";r.d(t,{GoogleAnalytics:()=>o,Sz:()=>c,k8:()=>l});var a=r(5155),i=r(2115),n=r(5695),s=r(1335);function o(){let e=(0,n.usePathname)();return((0,i.useEffect)(()=>{(0,s.initGA)();let e=(0,s.initScrollTracking)(),t=(0,s.initTimeTracking)();return()=>{e&&e(),t&&t()}},[]),(0,i.useEffect)(()=>{(0,s.trackPageView)(e)},[e]),s.GA_MEASUREMENT_ID&&"G-XXXXXXXXXX"!==s.GA_MEASUREMENT_ID)?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("script",{async:!0,src:"https://www.googletagmanager.com/gtag/js?id=".concat(s.GA_MEASUREMENT_ID)}),(0,a.jsx)("script",{dangerouslySetInnerHTML:{__html:"\n            window.dataLayer = window.dataLayer || [];\n            function gtag(){dataLayer.push(arguments);}\n            gtag('js', new Date());\n            gtag('config', '".concat(s.GA_MEASUREMENT_ID,"', {\n              page_title: document.title,\n              page_location: window.location.href,\n            });\n          ")}})]}):null}function c(){return{trackClick:(e,t,a,i,n)=>{Promise.resolve().then(r.bind(r,1335)).then(r=>{let{trackAffiliateClick:s}=r;s(e,t,a,i,n)})}}}function l(){return{trackView:(e,t,a,i,n)=>{Promise.resolve().then(r.bind(r,1335)).then(r=>{let{trackProductView:s}=r;s(e,t,a,i,n)})}}}},846:(e,t,r)=>{"use strict";r.d(t,{Header:()=>v});var a=r(5155),i=r(2115),n=r(6874),s=r.n(n),o=r(285),c=r(6126),l=r(1539),d=r(9946);let m=(0,d.A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]),u=(0,d.A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]]);var h=r(4416);let g=(0,d.A)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]]),p=[{name:"Reviews",href:"/reviews",description:"In-depth product reviews"},{name:"Categories",href:"#",description:"Browse by product type",children:[{name:"Portable Power Stations",href:"/category/portable-power-stations"},{name:"Solar Generators",href:"/category/solar-generators"},{name:"Home Battery Systems",href:"/category/home-battery-systems"}]},{name:"Guides",href:"/guides",description:"Buying guides and tutorials"},{name:"Best Of 2025",href:"/best-portable-power-stations-2025",badge:"Popular"},{name:"About",href:"/about"}];function v(){let[e,t]=(0,i.useState)(!1),[r,n]=(0,i.useState)(null);return(0,a.jsx)("header",{className:"sticky top-0 z-50 bg-white/95 backdrop-blur-sm border-b border-gray-200",children:(0,a.jsxs)("nav",{className:"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8",children:[(0,a.jsxs)("div",{className:"flex h-16 items-center justify-between",children:[(0,a.jsx)("div",{className:"flex items-center",children:(0,a.jsxs)(s(),{href:"/",className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"flex items-center justify-center w-8 h-8 bg-primary-600 rounded-lg",children:(0,a.jsx)(l.A,{className:"w-5 h-5 text-white"})}),(0,a.jsx)("span",{className:"text-xl font-bold text-gray-900",children:"OffGridFlux"})]})}),(0,a.jsx)("div",{className:"hidden md:flex items-center space-x-8",children:p.map(e=>(0,a.jsx)("div",{className:"relative",children:e.children?(0,a.jsxs)("div",{className:"relative",onMouseEnter:()=>n(e.name),onMouseLeave:()=>n(null),children:[(0,a.jsxs)("button",{className:"flex items-center text-gray-700 hover:text-primary-600 font-medium transition-colors",children:[e.name,(0,a.jsx)(m,{className:"ml-1 w-4 h-4"})]}),r===e.name&&(0,a.jsx)("div",{className:"absolute top-full left-0 mt-2 w-64 bg-white rounded-lg shadow-lg border border-gray-200 py-2",children:e.children.map(e=>(0,a.jsx)(s(),{href:e.href,className:"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 hover:text-primary-600",children:e.name},e.name))})]}):(0,a.jsxs)(s(),{href:e.href,className:"flex items-center text-gray-700 hover:text-primary-600 font-medium transition-colors",children:[e.name,e.badge&&(0,a.jsx)(c.E,{className:"ml-2 bg-warning-100 text-warning-700 text-xs",children:e.badge})]})},e.name))}),(0,a.jsxs)("div",{className:"hidden md:flex items-center space-x-4",children:[(0,a.jsxs)(o.$,{variant:"outline",size:"sm",className:"flex items-center",children:[(0,a.jsx)(u,{className:"w-4 h-4 mr-2"}),"Search"]}),(0,a.jsx)(o.$,{size:"sm",className:"bg-primary-600 hover:bg-primary-700",children:"Newsletter"})]}),(0,a.jsx)("div",{className:"md:hidden",children:(0,a.jsx)("button",{type:"button",className:"text-gray-700 hover:text-primary-600",onClick:()=>t(!e),children:e?(0,a.jsx)(h.A,{className:"w-6 h-6"}):(0,a.jsx)(g,{className:"w-6 h-6"})})})]}),e&&(0,a.jsx)("div",{className:"md:hidden border-t border-gray-200 py-4",children:(0,a.jsxs)("div",{className:"space-y-4",children:[p.map(e=>(0,a.jsx)("div",{children:e.children?(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-gray-900 font-medium py-2",children:e.name}),(0,a.jsx)("div",{className:"pl-4 space-y-2",children:e.children.map(e=>(0,a.jsx)(s(),{href:e.href,className:"block text-gray-600 hover:text-primary-600 py-1",onClick:()=>t(!1),children:e.name},e.name))})]}):(0,a.jsxs)(s(),{href:e.href,className:"flex items-center text-gray-700 hover:text-primary-600 font-medium py-2",onClick:()=>t(!1),children:[e.name,e.badge&&(0,a.jsx)(c.E,{className:"ml-2 bg-warning-100 text-warning-700 text-xs",children:e.badge})]})},e.name)),(0,a.jsxs)("div",{className:"pt-4 border-t border-gray-200 space-y-2",children:[(0,a.jsxs)(o.$,{variant:"outline",size:"sm",className:"w-full justify-center",children:[(0,a.jsx)(u,{className:"w-4 h-4 mr-2"}),"Search"]}),(0,a.jsx)(o.$,{size:"sm",className:"w-full bg-primary-600 hover:bg-primary-700",children:"Newsletter"})]})]})})]})})}},1113:(e,t,r)=>{"use strict";r.d(t,{AffiliateDisclosure:()=>m});var a=r(5155),i=r(2115),n=r(6126),s=r(285),o=r(9946);let c=(0,o.A)("info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]]),l=(0,o.A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]]);var d=r(4416);function m(){let[e,t]=(0,i.useState)(!0),[r,o]=(0,i.useState)(!1);return e?(0,a.jsx)("div",{className:"bg-primary-50 border-b border-primary-200 relative",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 py-3",children:(0,a.jsxs)("div",{className:"flex items-start gap-3",children:[(0,a.jsx)("div",{className:"flex-shrink-0 mt-0.5",children:(0,a.jsx)(c,{className:"w-5 h-5 text-primary-600"})}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsx)("div",{className:"flex items-center gap-2 mb-1",children:(0,a.jsx)(n.E,{variant:"outline",className:"text-xs border-primary-300 text-primary-700",children:"FTC Disclosure"})}),(0,a.jsxs)("div",{className:"text-sm text-primary-800",children:[(0,a.jsx)("p",{className:"font-medium mb-1",children:"Affiliate Disclosure: We earn commissions from qualifying purchases."}),r?(0,a.jsxs)("div",{className:"text-primary-700 space-y-2",children:[(0,a.jsx)("p",{children:"OffGridFlux participates in affiliate programs with Amazon, Jackery, Goal Zero, EcoFlow, and other retailers. When you click on links to products and make a purchase, we may receive a commission at no additional cost to you."}),(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Our Promise:"})," We only recommend products we've personally tested or would use ourselves. Our reviews are based on real-world testing and are not influenced by affiliate relationships. We purchase most products at full retail price for testing."]}),(0,a.jsxs)("p",{children:["Commission earnings help support our independent testing lab and keep our content free for readers. For complete details, see our"," ",(0,a.jsxs)("a",{href:"/affiliate-disclosure",className:"underline hover:no-underline font-medium inline-flex items-center gap-1",children:["full affiliate disclosure policy",(0,a.jsx)(l,{className:"w-3 h-3"})]}),"."]}),(0,a.jsx)("button",{onClick:()=>o(!1),className:"underline hover:no-underline font-medium text-xs",children:"Show less"})]}):(0,a.jsxs)("p",{className:"text-primary-700",children:["As an Amazon Associate and affiliate partner, we earn from qualifying purchases. This doesn't affect our review process or recommendations."," ",(0,a.jsx)("button",{onClick:()=>o(!0),className:"underline hover:no-underline font-medium",children:"Read full disclosure"})]})]})]}),(0,a.jsx)(s.$,{variant:"ghost",size:"sm",onClick:()=>t(!1),className:"flex-shrink-0 h-8 w-8 p-0 text-primary-600 hover:bg-primary-100","aria-label":"Close disclosure",children:(0,a.jsx)(d.A,{className:"w-4 h-4"})})]})})}):null}},1335:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GA_MEASUREMENT_ID:()=>a,initGA:()=>i,initScrollTracking:()=>k,initTimeTracking:()=>j,setCustomDimensions:()=>_,trackABTest:()=>b,trackAffiliateClick:()=>o,trackContentEngagement:()=>m,trackConversionFunnel:()=>v,trackEmailInteraction:()=>x,trackError:()=>y,trackEvent:()=>s,trackNewsletterSignup:()=>l,trackPageView:()=>n,trackPerformanceMetric:()=>w,trackProductView:()=>c,trackPurchaseIntent:()=>g,trackScrollDepth:()=>u,trackSearch:()=>d,trackSocialShare:()=>f,trackTimeOnPage:()=>h,trackUserPreference:()=>p});let a="G-XXXXXXXXXX",i=()=>{let e=document.createElement("script");e.src="https://www.googletagmanager.com/gtag/js?id=".concat(a),e.async=!0,document.head.appendChild(e),window.dataLayer=window.dataLayer||[],window.gtag=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];window.dataLayer.push(t)},window.gtag("js",new Date),window.gtag("config",a,{page_title:document.title,page_location:window.location.href})},n=(e,t)=>{window.gtag&&window.gtag("config",a,{page_title:t||document.title,page_location:e})},s=(e,t,r,a,i)=>{window.gtag&&window.gtag("event",e,{event_category:t,event_label:r,value:a,...i})},o=(e,t,r,a,i)=>{s("affiliate_click","ecommerce",e,a,{product_id:t,product_name:e,product_brand:r,product_price:a,affiliate_url:i,currency:"USD"})},c=(e,t,r,a,i)=>{window.gtag&&window.gtag("event","view_item",{currency:"USD",value:a,items:[{item_id:t,item_name:e,item_brand:r,item_category:i,price:a,quantity:1}]})},l=e=>{s("newsletter_signup","engagement",e,void 0,{signup_source:e})},d=(e,t)=>{s("search","engagement",e,t,{search_term:e,results_count:t})},m=(e,t,r,a)=>{s(r,"content",t,a,{content_type:e,content_id:t})},u=(e,t)=>{s("scroll_depth","engagement",t,e,{scroll_percentage:e,page_path:t})},h=(e,t)=>{s("time_on_page","engagement",t,e,{time_seconds:e,page_path:t})},g=(e,t,r,a,i)=>{window.gtag&&window.gtag("event","view_review"===i?"view_item":"click_affiliate"===i?"select_item":"add_to_cart",{currency:"USD",value:a,items:[{item_id:t,item_name:e,item_brand:r,item_category:"Portable Power Station",price:a,quantity:1}]})},p=(e,t)=>{s("user_preference","personalization",e,void 0,{preference_type:e,preference_value:t})},v=(e,t,r)=>{s("conversion_funnel","ecommerce",e,void 0,{funnel_stage:e,traffic_source:t,product_id:r})},f=(e,t,r)=>{s("social_share","engagement",e,void 0,{social_platform:e,content_type:t,content_id:r})},x=(e,t,r)=>{s("email_interaction","engagement",e,void 0,{email_action:e,campaign_id:t,email_type:r})},w=(e,t,r)=>{s("performance_metric","technical",e,t,{metric_name:e,metric_value:t,page_path:r})},y=function(e,t,r){let a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"medium";s("error","technical",e,void 0,{error_type:e,error_message:t,error_severity:a,page_path:r})},b=(e,t,r)=>{s("ab_test","optimization",e,void 0,{test_name:e,test_variant:t,test_action:r})},_=e=>{window.gtag&&window.gtag("config",a,{custom_map:e})},k=()=>{let e=0,t=[25,50,75,90,100],r=new Set,a=()=>{let a=Math.round(window.scrollY/(document.documentElement.scrollHeight-window.innerHeight)*100);a>e&&(e=a,t.forEach(e=>{a>=e&&!r.has(e)&&(r.add(e),u(e,window.location.pathname))}))};return window.addEventListener("scroll",a,{passive:!0}),()=>{window.removeEventListener("scroll",a)}},j=()=>{let e=Date.now(),t=()=>{h(Math.round((Date.now()-e)/1e3),window.location.pathname)};return document.addEventListener("visibilitychange",()=>{"hidden"===document.visibilityState&&t()}),window.addEventListener("beforeunload",t),t}},1666:e=>{e.exports={style:{fontFamily:"'Inter', 'Inter Fallback'",fontStyle:"normal"},className:"__className_e8ce0c",variable:"__variable_e8ce0c"}},4416:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(9946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},5695:(e,t,r)=>{"use strict";var a=r(8999);r.o(a,"usePathname")&&r.d(t,{usePathname:function(){return a.usePathname}})},6126:(e,t,r)=>{"use strict";r.d(t,{E:()=>c});var a=r(5155);r(2115);var i=r(4624),n=r(2085),s=r(9434);let o=(0,n.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function c(e){let{className:t,variant:r,asChild:n=!1,...c}=e,l=n?i.DX:"span";return(0,a.jsx)(l,{"data-slot":"badge",className:(0,s.cn)(o({variant:r}),t),...c})}},8308:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6874,23)),Promise.resolve().then(r.t.bind(r,1666,23)),Promise.resolve().then(r.t.bind(r,347,23)),Promise.resolve().then(r.bind(r,1113)),Promise.resolve().then(r.bind(r,395)),Promise.resolve().then(r.bind(r,846))},9434:(e,t,r)=>{"use strict";r.d(t,{cn:()=>n});var a=r(2596),i=r(9688);function n(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,i.QP)((0,a.$)(t))}}},e=>{var t=t=>e(e.s=t);e.O(0,[258,497,800,441,684,358],()=>t(8308)),_N_E=e.O()}]);