// OffGridFlux - Newsletter Signup Component
// Email subscription form with benefits

'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Mail, CheckCircle, Gift, Zap, FileText } from 'lucide-react';

const benefits = [
  {
    icon: Zap,
    title: 'Latest Reviews',
    description: 'Get notified when we publish new product reviews'
  },
  {
    icon: Gift,
    title: 'Exclusive Deals',
    description: 'Access to subscriber-only discounts and promotions'
  },
  {
    icon: FileText,
    title: 'Buying Guides',
    description: 'In-depth guides to help you make the right choice'
  }
];

export function NewsletterSignup() {
  const [email, setEmail] = useState('');
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!email) return;

    setIsLoading(true);
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    setIsSubmitted(true);
    setIsLoading(false);
    setEmail('');
  };

  if (isSubmitted) {
    return (
      <section className="py-16 bg-primary-600 text-white">
        <div className="max-w-4xl mx-auto px-4 text-center">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-green-500 rounded-full mb-6">
            <CheckCircle className="w-8 h-8 text-white" />
          </div>
          <h2 className="text-3xl font-bold mb-4">
            Welcome to the OffGridFlux Community! 🎉
          </h2>
          <p className="text-xl text-primary-100 mb-6">
            Check your email for a confirmation link and your welcome guide.
          </p>
          <Button 
            variant="outline" 
            className="border-white text-white hover:bg-white hover:text-primary-600"
            onClick={() => setIsSubmitted(false)}
          >
            Subscribe Another Email
          </Button>
        </div>
      </section>
    );
  }

  return (
    <section className="py-20 bg-gradient-to-br from-neutral-900 via-neutral-800 to-neutral-900 relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-[linear-gradient(to_right,#ffffff08_1px,transparent_1px),linear-gradient(to_bottom,#ffffff08_1px,transparent_1px)] bg-[size:4rem_4rem]"></div>

      <div className="max-w-6xl mx-auto px-4 relative z-10">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">

          {/* Left Column - Content */}
          <div>
            <div className="inline-flex items-center gap-2 bg-primary-500/20 text-primary-300 rounded-full px-4 py-2 text-sm font-medium mb-6 border border-primary-500/30">
              <Mail className="w-4 h-4" />
              Free Newsletter
            </div>

            <h2 className="text-4xl md:text-5xl font-bold mb-6 text-white leading-tight">
              Stay Powered Up with Expert Insights
            </h2>

            <p className="text-xl text-neutral-300 mb-8 leading-relaxed">
              Join 10,000+ off-grid enthusiasts getting the latest reviews, buying guides,
              and exclusive deals delivered to their inbox.
            </p>

            {/* Benefits List */}
            <div className="space-y-6">
              {benefits.map((benefit, index) => {
                const IconComponent = benefit.icon;

                return (
                  <div key={index} className="flex items-start gap-4">
                    <div className="flex-shrink-0 w-12 h-12 bg-gradient-to-br from-primary-500 to-primary-600 rounded-xl flex items-center justify-center shadow-lg">
                      <IconComponent className="w-6 h-6 text-white" />
                    </div>
                    <div>
                      <h3 className="font-bold text-white mb-2 text-lg">
                        {benefit.title}
                      </h3>
                      <p className="text-neutral-400 leading-relaxed">
                        {benefit.description}
                      </p>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Right Column - Form */}
          <div className="bg-white rounded-3xl p-8 shadow-2xl border border-neutral-200">
            <div className="text-center mb-6">
              <h3 className="text-2xl font-bold text-neutral-900 mb-2">
                Get Expert Insights
              </h3>
              <p className="text-neutral-600">
                Join thousands of off-grid enthusiasts
              </p>
            </div>

            <form onSubmit={handleSubmit} className="space-y-6">
              <div>
                <label htmlFor="email" className="block text-sm font-semibold text-neutral-700 mb-3">
                  Email Address
                </label>
                <Input
                  id="email"
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="Enter your email address"
                  required
                  className="h-12 bg-neutral-50 border-neutral-200 text-neutral-900 placeholder:text-neutral-500 focus:border-primary-500 focus:ring-primary-500 rounded-xl"
                />
              </div>

              <Button
                type="submit"
                disabled={isLoading || !email}
                className="w-full h-12 bg-gradient-to-r from-primary-600 to-primary-700 hover:from-primary-700 hover:to-primary-800 text-white font-bold rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105"
              >
                {isLoading ? (
                  <div className="flex items-center gap-2">
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    Subscribing...
                  </div>
                ) : (
                  'Get Free Updates'
                )}
              </Button>

              <p className="text-xs text-neutral-500 text-center leading-relaxed">
                No spam, ever. Unsubscribe anytime with one click.
                <br />
                By subscribing, you agree to our{' '}
                <a href="/privacy" className="text-primary-600 underline hover:text-primary-700">
                  Privacy Policy
                </a>
                .
              </p>
            </form>

            {/* Social Proof */}
            <div className="mt-6 pt-6 border-t border-neutral-200">
              <div className="flex items-center justify-center gap-6 text-sm">
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span className="text-neutral-600 font-medium">10,000+ subscribers</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                  <span className="text-neutral-600 font-medium">4.9★ rating</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
