(()=>{var e={};e.id=490,e.ids=[490],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1075:(e,t,r)=>{"use strict";r.d(t,{ProductCard:()=>i});var s=r(2907);let i=(0,s.registerClientReference)(function(){throw Error("Attempted to call ProductCard() from the server but ProductCard is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\ProductCard.tsx","ProductCard");(0,s.registerClientReference)(function(){throw Error("Attempted to call ProductCardSkeleton() from the server but ProductCardSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\ProductCard.tsx","ProductCardSkeleton")},1107:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>N,generateMetadata:()=>w});var s=r(7413),i=r(9916),a=r(6621),n=r(2480),l=r.n(n),o=r(84),d=r(3469),c=r(7539),u=r(1227),m=r(9972),p=r(5805),x=r(3488);function g({product:e,valueScore:t}){return(0,s.jsx)("section",{className:"bg-gradient-to-r from-gray-50 to-white py-16",children:(0,s.jsx)("div",{className:"max-w-7xl mx-auto px-4",children:(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center",children:[(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"aspect-square bg-white rounded-2xl shadow-lg p-8 border",children:e.product_image_url?(0,s.jsx)(l(),{src:e.product_image_url,alt:e.product_name,fill:!0,className:"object-contain p-8"}):(0,s.jsx)("div",{className:"flex items-center justify-center h-full",children:(0,s.jsx)(c.A,{className:"w-32 h-32 text-gray-300"})})}),e.discount_percent&&(0,s.jsxs)(o.E,{className:"absolute top-4 right-4 bg-red-500 text-white text-lg px-3 py-1",children:[e.discount_percent,"% OFF"]}),e.tested_by_offgridflux&&(0,s.jsxs)(o.E,{className:"absolute bottom-4 left-4 bg-green-500 text-white flex items-center gap-2",children:[(0,s.jsx)(u.A,{className:"w-4 h-4"}),"Tested by OffGridFlux"]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(o.E,{className:"mb-4 bg-primary-100 text-primary-700",children:e.brand}),(0,s.jsx)("h1",{className:"text-4xl md:text-5xl font-bold text-gray-900 mb-6 leading-tight",children:e.product_name}),e.offgridflux_rating&&(0,s.jsxs)("div",{className:"flex items-center gap-4 mb-6",children:[(0,s.jsx)("div",{className:"flex items-center gap-2",children:[void 0,void 0,void 0,void 0,void 0].map((t,r)=>(0,s.jsx)(m.A,{className:`w-5 h-5 ${r<Math.floor(e.offgridflux_rating)?"text-yellow-400 fill-current":"text-gray-300"}`},r))}),(0,s.jsxs)("span",{className:"text-lg font-medium text-gray-900",children:[e.offgridflux_rating,"/10"]}),(0,s.jsx)("span",{className:"text-gray-500",children:"OffGridFlux Rating"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-6 mb-8",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)("div",{className:"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center",children:(0,s.jsx)(c.A,{className:"w-6 h-6 text-blue-600"})}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"text-2xl font-bold text-gray-900",children:[e.capacity_wh,"Wh"]}),(0,s.jsx)("div",{className:"text-gray-500 text-sm",children:"Capacity"})]})]}),(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)("div",{className:"w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center",children:(0,s.jsx)(p.A,{className:"w-6 h-6 text-yellow-600"})}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"text-2xl font-bold text-gray-900",children:[e.max_output_w,"W"]}),(0,s.jsx)("div",{className:"text-gray-500 text-sm",children:"Output Power"})]})]})]}),(0,s.jsxs)("div",{className:"mb-8",children:[(0,s.jsxs)("div",{className:"flex items-baseline gap-4 mb-2",children:[(0,s.jsxs)("div",{className:"text-4xl font-bold text-gray-900",children:["$",e.current_price_usd]}),e.msrp_usd&&e.msrp_usd!==e.current_price_usd&&(0,s.jsxs)("div",{className:"text-2xl text-gray-500 line-through",children:["$",e.msrp_usd]})]}),t&&(0,s.jsxs)("div",{className:"text-green-600 font-medium",children:["Excellent value: ",t," Wh per dollar"]})]}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[e.affiliate_url&&(0,s.jsx)(d.$,{size:"lg",className:"bg-primary-600 hover:bg-primary-700",asChild:!0,children:(0,s.jsxs)("a",{href:e.affiliate_url,target:"_blank",rel:"noopener noreferrer",className:"flex items-center gap-2",children:[(0,s.jsx)("span",{children:"Check Current Price"}),(0,s.jsx)(x.A,{className:"w-4 h-4"})]})}),(0,s.jsx)(d.$,{size:"lg",variant:"outline",children:"Compare Similar Products"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-6 mt-6 text-sm text-gray-500",children:[(0,s.jsx)("span",{children:"✓ Free shipping"}),(0,s.jsx)("span",{children:"✓ 30-day returns"}),(0,s.jsxs)("span",{children:["✓ ",e.warranty_years,"-year warranty"]})]})]})]})})})}var f=r(6371),h=r(349),b=r(7584),v=r(1075);function y({products:e}){return 0===e.length?null:(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:e.map(e=>(0,s.jsx)(v.ProductCard,{product:e},e.id))})}var _=r(1190);function j({product:e}){let t={"@context":"https://schema.org","@type":"Product",name:e.product_name,brand:{"@type":"Brand",name:e.brand},description:e.meta_description||`${e.product_name} - ${e.capacity_wh}Wh portable power station`,sku:e.slug,offers:{"@type":"Offer",price:e.current_price_usd,priceCurrency:"USD",availability:"https://schema.org/InStock",url:e.affiliate_url},aggregateRating:e.offgridflux_rating?{"@type":"AggregateRating",ratingValue:e.offgridflux_rating,bestRating:10,worstRating:1,ratingCount:1}:void 0};return(0,s.jsx)("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify(t)}})}async function w({params:e}){let t=await e,r=(0,a.UU)(),{data:s}=await r.from("product_specs").select("*").eq("slug",t.slug).single();if(!s)return{title:"Product Not Found - OffGridFlux",description:"The requested product could not be found."};let i=s.meta_title||`${s.product_name} Review | OffGridFlux`,n=s.meta_description||`In-depth review of the ${s.product_name}. ${s.capacity_wh}Wh capacity, ${s.max_output_w}W output. Real-world testing, pros & cons, and buying guide.`;return{title:i,description:n,keywords:s.keywords?.join(", ")||`${s.brand}, ${s.model}, portable power station, review`,openGraph:{title:i,description:n,type:"article",url:`https://offgridflux.com/product/${t.slug}`,images:s.product_image_url?[{url:s.product_image_url,width:1200,height:630,alt:s.product_name}]:[]},twitter:{card:"summary_large_image",title:i,description:n,images:s.product_image_url?[s.product_image_url]:[]}}}async function N({params:e}){let t=await e,r=(0,a.UU)(),{data:n,error:l}=await r.from("product_specs").select("*").eq("slug",t.slug).single();(l||!n)&&(0,i.notFound)();let{data:o}=await r.from("product_specs").select("*").neq("id",n.id).or(`brand.eq.${n.brand},and(capacity_wh.gte.${.8*n.capacity_wh},capacity_wh.lte.${1.2*n.capacity_wh})`).eq("availability_status","In Stock").limit(4),d=n.current_price_usd?(n.capacity_wh/parseFloat(n.current_price_usd)).toFixed(2):null;return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(j,{product:n}),(0,s.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,s.jsx)(_.AffiliateDisclosure,{}),(0,s.jsx)(g,{product:n,valueScore:d}),(0,s.jsx)("div",{className:"max-w-7xl mx-auto px-4 py-12",children:(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,s.jsxs)("div",{className:"lg:col-span-2 space-y-8",children:[(0,s.jsxs)("section",{className:"bg-white rounded-lg shadow-sm p-6",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Overview"}),(0,s.jsx)("p",{className:"text-gray-600 leading-relaxed",children:n.product_description||`The ${n.product_name} is a ${n.capacity_wh}Wh portable power station with ${n.max_output_w}W output capacity. 
                    Perfect for ${n.use_cases?.join(", ")||"camping, emergencies, and off-grid power needs"}.`}),n.key_features&&n.key_features.length>0&&(0,s.jsxs)("div",{className:"mt-6",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:"Key Features"}),(0,s.jsx)("ul",{className:"grid grid-cols-1 md:grid-cols-2 gap-2",children:n.key_features.map((e,t)=>(0,s.jsxs)("li",{className:"flex items-center text-gray-600",children:[(0,s.jsx)("span",{className:"w-2 h-2 bg-green-500 rounded-full mr-3"}),e]},t))})]})]}),(0,s.jsxs)("section",{className:"bg-white rounded-lg shadow-sm p-6",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Specifications"}),(0,s.jsx)(f.i,{product:n})]}),(n.pros?.length||n.cons?.length)&&(0,s.jsxs)("section",{className:"bg-white rounded-lg shadow-sm p-6",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Pros & Cons"}),(0,s.jsx)(h.R,{pros:n.pros||[],cons:n.cons||[]})]}),(0,s.jsxs)("section",{className:"bg-white rounded-lg shadow-sm p-6",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Runtime Calculator"}),(0,s.jsx)(b.RuntimeCalculator,{product:n})]})]}),(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsx)("div",{className:"bg-white rounded-lg shadow-sm p-6 sticky top-6",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsxs)("div",{className:"mb-4",children:[n.discount_percent&&(0,s.jsxs)("span",{className:"inline-block bg-red-100 text-red-800 text-sm font-medium px-2 py-1 rounded mb-2",children:[n.discount_percent,"% OFF"]}),(0,s.jsxs)("div",{className:"text-3xl font-bold text-gray-900",children:["$",n.current_price_usd]}),n.msrp_usd&&n.msrp_usd!==n.current_price_usd&&(0,s.jsxs)("div",{className:"text-lg text-gray-500 line-through",children:["$",n.msrp_usd]})]}),(0,s.jsx)("a",{href:n.affiliate_url,target:"_blank",rel:"noopener noreferrer",className:"w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-200 inline-block",children:"Check Current Price"}),(0,s.jsx)("p",{className:"text-xs text-gray-500 mt-2",children:"Price and availability subject to change"})]})}),(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-sm p-6",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Quick Stats"}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{className:"text-gray-600",children:"Capacity"}),(0,s.jsxs)("span",{className:"font-medium",children:[n.capacity_wh,"Wh"]})]}),(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{className:"text-gray-600",children:"Output"}),(0,s.jsxs)("span",{className:"font-medium",children:[n.max_output_w,"W"]})]}),(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{className:"text-gray-600",children:"Weight"}),(0,s.jsxs)("span",{className:"font-medium",children:[n.weight_lbs,"lbs"]})]}),(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{className:"text-gray-600",children:"Battery"}),(0,s.jsx)("span",{className:"font-medium",children:n.battery_type})]}),d&&(0,s.jsxs)("div",{className:"flex justify-between border-t pt-3",children:[(0,s.jsx)("span",{className:"text-gray-600",children:"Value Score"}),(0,s.jsxs)("span",{className:"font-medium text-green-600",children:[d," Wh/$"]})]})]})]})]})]})}),o&&o.length>0&&(0,s.jsx)("section",{className:"py-12 bg-white",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto px-4",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-8",children:"Related Products"}),(0,s.jsx)(y,{products:o})]})})]})]})}},1122:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"warnOnce",{enumerable:!0,get:function(){return r}});let r=e=>{}},1142:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(6373).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},1322:(e,t)=>{"use strict";function r(e){let{widthInt:t,heightInt:r,blurWidth:s,blurHeight:i,blurDataURL:a,objectFit:n}=e,l=s?40*s:t,o=i?40*i:r,d=l&&o?"viewBox='0 0 "+l+" "+o+"'":"";return"%3Csvg xmlns='http://www.w3.org/2000/svg' "+d+"%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='"+(d?"none":"contain"===n?"xMidYMid":"cover"===n?"xMidYMid slice":"none")+"' style='filter: url(%23b);' href='"+a+"'/%3E%3C/svg%3E"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImageBlurSvg",{enumerable:!0,get:function(){return r}})},1630:e=>{"use strict";e.exports=require("http")},1645:e=>{"use strict";e.exports=require("net")},1997:e=>{"use strict";e.exports=require("punycode")},2091:(e,t)=>{"use strict";function r(e){var t;let{config:r,src:s,width:i,quality:a}=e,n=a||(null==(t=r.qualities)?void 0:t.reduce((e,t)=>Math.abs(t-75)<Math.abs(e-75)?t:e))||75;return r.path+"?url="+encodeURIComponent(s)+"&w="+i+"&q="+n+(s.startsWith("/_next/static/media/"),"")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return s}}),r.__next_img_default=!0;let s=r},2480:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return o},getImageProps:function(){return l}});let s=r(2639),i=r(9131),a=r(9603),n=s._(r(2091));function l(e){let{props:t}=(0,i.getImgProps)(e,{defaultLoader:n.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[e,r]of Object.entries(t))void 0===r&&delete t[e];return{props:t}}let o=a.Image},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},4075:e=>{"use strict";e.exports=require("zlib")},4631:e=>{"use strict";e.exports=require("tls")},4735:e=>{"use strict";e.exports=require("events")},4780:(e,t,r)=>{"use strict";r.d(t,{cn:()=>a});var s=r(9384),i=r(2348);function a(...e){return(0,i.QP)((0,s.$)(e))}},4897:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var s=r(5239),i=r(8088),a=r(8170),n=r.n(a),l=r(893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);r.d(t,o);let d={children:["",{children:["product",{children:["[slug]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,1107)),"C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\app\\product\\[slug]\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,4431)),"C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\app\\product\\[slug]\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/product/[slug]/page",pathname:"/product/[slug]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},4973:(e,t,r)=>{"use strict";r.d(t,{ProductCard:()=>m});var s=r(687),i=r(5814),a=r.n(i),n=r(474);r(3210);var l=r(7689),o=r(4494),d=r(5583),c=r(3694),u=r(4398);function m({product:e}){let{trackClick:t}=(0,l.Sz)(),{trackView:r}=(0,l.k8)(),i=e.current_price_usd?(e.capacity_wh/parseFloat(String(e.current_price_usd))).toFixed(2):null;return(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-soft hover:shadow-medium transition-shadow duration-300 overflow-hidden",children:[(0,s.jsxs)("div",{className:"relative h-48 bg-gray-100",children:[e.product_image_url?(0,s.jsx)(n.default,{src:e.product_image_url,alt:e.product_name,fill:!0,className:"object-contain p-4"}):(0,s.jsx)("div",{className:"flex items-center justify-center h-full",children:(0,s.jsx)(o.A,{className:"w-16 h-16 text-gray-400"})}),e.discount_percent&&(0,s.jsxs)("div",{className:"absolute top-2 right-2 bg-red-500 text-white text-sm font-medium px-2 py-1 rounded",children:[e.discount_percent,"% OFF"]}),(0,s.jsx)("div",{className:"absolute top-2 left-2 bg-white/90 backdrop-blur-sm text-gray-700 text-sm font-medium px-2 py-1 rounded",children:e.brand})]}),(0,s.jsxs)("div",{className:"p-4",children:[(0,s.jsx)("h3",{className:"font-semibold text-gray-900 mb-2 line-clamp-2",children:(0,s.jsx)(a(),{href:`/product/${e.slug}`,className:"hover:text-primary-600 transition-colors",children:e.product_name})}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-2 mb-3 text-sm",children:[(0,s.jsxs)("div",{className:"flex items-center text-gray-600",children:[(0,s.jsx)(o.A,{className:"w-4 h-4 mr-1"}),e.capacity_wh,"Wh"]}),(0,s.jsxs)("div",{className:"flex items-center text-gray-600",children:[(0,s.jsx)(d.A,{className:"w-4 h-4 mr-1"}),e.max_output_w,"W"]}),e.weight_lbs&&(0,s.jsxs)("div",{className:"flex items-center text-gray-600",children:[(0,s.jsx)(c.A,{className:"w-4 h-4 mr-1"}),e.weight_lbs,"lbs"]}),e.battery_type&&(0,s.jsx)("div",{className:"text-gray-600 text-xs",children:e.battery_type})]}),e.offgridflux_rating&&(0,s.jsxs)("div",{className:"flex items-center mb-3",children:[(0,s.jsx)("div",{className:"flex items-center",children:[void 0,void 0,void 0,void 0,void 0].map((t,r)=>(0,s.jsx)(u.A,{className:`w-4 h-4 ${r<Math.floor(e.offgridflux_rating)?"text-yellow-400 fill-current":"text-gray-300"}`},r))}),(0,s.jsxs)("span",{className:"ml-2 text-sm text-gray-600",children:[e.offgridflux_rating,"/10"]})]}),e.key_features&&e.key_features.length>0&&(0,s.jsx)("div",{className:"mb-3",children:(0,s.jsx)("ul",{className:"text-sm text-gray-600 space-y-1",children:e.key_features.slice(0,2).map((e,t)=>(0,s.jsxs)("li",{className:"flex items-center",children:[(0,s.jsx)("span",{className:"w-1.5 h-1.5 bg-green-500 rounded-full mr-2 flex-shrink-0"}),e]},t))})}),(0,s.jsxs)("div",{className:"border-t pt-3",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"text-2xl font-bold text-gray-900",children:["$",e.current_price_usd]}),e.msrp_usd&&e.msrp_usd!==e.current_price_usd&&(0,s.jsxs)("div",{className:"text-sm text-gray-500 line-through",children:["$",e.msrp_usd]})]}),i&&(0,s.jsxs)("div",{className:"text-right",children:[(0,s.jsx)("div",{className:"text-xs text-gray-500",children:"Value Score"}),(0,s.jsxs)("div",{className:"text-sm font-medium text-green-600",children:[i," Wh/$"]})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(a(),{href:`/product/${e.slug}`,className:"block w-full bg-primary-600 hover:bg-primary-700 text-white text-center py-2 px-4 rounded-lg transition-colors duration-200 font-medium",children:"View Review"}),e.affiliate_url&&(0,s.jsx)("a",{href:e.affiliate_url,target:"_blank",rel:"noopener noreferrer",onClick:()=>{t(e.product_name,e.slug,e.brand,e.current_price_usd||0,e.affiliate_url||"")},className:"block w-full bg-gray-100 hover:bg-gray-200 text-gray-700 text-center py-2 px-4 rounded-lg transition-colors duration-200 text-sm",children:"Check Price"})]})]})]})]})}},5511:e=>{"use strict";e.exports=require("crypto")},5591:e=>{"use strict";e.exports=require("https")},6621:(e,t,r)=>{"use strict";r.d(t,{KF:()=>o,Ne:()=>l,UU:()=>n});var s=r(6345);let i="https://binxxcezkmkwvjbyiatc.supabase.co",a="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJpbnh4Y2V6a21rd3ZqYnlpYXRjIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg2NTc1NzEsImV4cCI6MjA2NDIzMzU3MX0.TwT5UmfkLBFH9LgutJ3wEqLrkhNMLYlJ0-Dnlf-fFPw";if(!i||!a)throw Error("Missing Supabase environment variables");let n=()=>(0,s.UU)(i,a,{auth:{persistSession:!1}}),l=async(e=6)=>n().from("product_specs").select("*").eq("availability_status","In Stock").not("offgridflux_rating","is",null).order("offgridflux_rating",{ascending:!1}).limit(e),o=async(e=5)=>{let t=n(),{data:r}=await t.from("product_specs").select("*").eq("availability_status","In Stock").not("current_price_usd","is",null).not("capacity_wh","is",null);return r?{data:r.map(e=>({...e,valueScore:e.capacity_wh/parseFloat(e.current_price_usd)})).sort((e,t)=>t.valueScore-e.valueScore).slice(0,e),error:null}:{data:null,error:"No products found"}}},6834:(e,t,r)=>{"use strict";r.d(t,{E:()=>o});var s=r(687);r(3210);var i=r(1391),a=r(4224),n=r(4780);let l=(0,a.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function o({className:e,variant:t,asChild:r=!1,...a}){let o=r?i.DX:"span";return(0,s.jsx)(o,{"data-slot":"badge",className:(0,n.cn)(l({variant:t}),e),...a})}},7894:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{VALID_LOADERS:function(){return r},imageConfigDefault:function(){return s}});let r=["default","imgix","cloudinary","akamai","custom"],s={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",localPatterns:void 0,remotePatterns:[],qualities:void 0,unoptimized:!1}},7910:e=>{"use strict";e.exports=require("stream")},8064:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6533,23)),Promise.resolve().then(r.bind(r,9466)),Promise.resolve().then(r.bind(r,4973)),Promise.resolve().then(r.bind(r,5200))},8688:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,9603,23)),Promise.resolve().then(r.bind(r,1190)),Promise.resolve().then(r.bind(r,1075)),Promise.resolve().then(r.bind(r,7584))},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9131:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImgProps",{enumerable:!0,get:function(){return o}}),r(1122);let s=r(1322),i=r(7894),a=["-moz-initial","fill","none","scale-down",void 0];function n(e){return void 0!==e.default}function l(e){return void 0===e?e:"number"==typeof e?Number.isFinite(e)?e:NaN:"string"==typeof e&&/^[0-9]+$/.test(e)?parseInt(e,10):NaN}function o(e,t){var r,o;let d,c,u,{src:m,sizes:p,unoptimized:x=!1,priority:g=!1,loading:f,className:h,quality:b,width:v,height:y,fill:_=!1,style:j,overrideSrc:w,onLoad:N,onLoadingComplete:k,placeholder:P="empty",blurDataURL:C,fetchPriority:S,decoding:E="async",layout:O,objectFit:R,objectPosition:z,lazyBoundary:I,lazyRoot:F,...q}=e,{imgConf:$,showAltText:A,blurComplete:M,defaultLoader:D}=t,U=$||i.imageConfigDefault;if("allSizes"in U)d=U;else{let e=[...U.deviceSizes,...U.imageSizes].sort((e,t)=>e-t),t=U.deviceSizes.sort((e,t)=>e-t),s=null==(r=U.qualities)?void 0:r.sort((e,t)=>e-t);d={...U,allSizes:e,deviceSizes:t,qualities:s}}if(void 0===D)throw Object.defineProperty(Error("images.loaderFile detected but the file is missing default export.\nRead more: https://nextjs.org/docs/messages/invalid-images-config"),"__NEXT_ERROR_CODE",{value:"E163",enumerable:!1,configurable:!0});let W=q.loader||D;delete q.loader,delete q.srcSet;let G="__next_img_default"in W;if(G){if("custom"===d.loader)throw Object.defineProperty(Error('Image with src "'+m+'" is missing "loader" prop.\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader'),"__NEXT_ERROR_CODE",{value:"E252",enumerable:!1,configurable:!0})}else{let e=W;W=t=>{let{config:r,...s}=t;return e(s)}}if(O){"fill"===O&&(_=!0);let e={intrinsic:{maxWidth:"100%",height:"auto"},responsive:{width:"100%",height:"auto"}}[O];e&&(j={...j,...e});let t={responsive:"100vw",fill:"100vw"}[O];t&&!p&&(p=t)}let T="",J=l(v),L=l(y);if((o=m)&&"object"==typeof o&&(n(o)||void 0!==o.src)){let e=n(m)?m.default:m;if(!e.src)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E460",enumerable:!1,configurable:!0});if(!e.height||!e.width)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E48",enumerable:!1,configurable:!0});if(c=e.blurWidth,u=e.blurHeight,C=C||e.blurDataURL,T=e.src,!_)if(J||L){if(J&&!L){let t=J/e.width;L=Math.round(e.height*t)}else if(!J&&L){let t=L/e.height;J=Math.round(e.width*t)}}else J=e.width,L=e.height}let V=!g&&("lazy"===f||void 0===f);(!(m="string"==typeof m?m:T)||m.startsWith("data:")||m.startsWith("blob:"))&&(x=!0,V=!1),d.unoptimized&&(x=!0),G&&!d.dangerouslyAllowSVG&&m.split("?",1)[0].endsWith(".svg")&&(x=!0);let X=l(b),B=Object.assign(_?{position:"absolute",height:"100%",width:"100%",left:0,top:0,right:0,bottom:0,objectFit:R,objectPosition:z}:{},A?{}:{color:"transparent"},j),Y=M||"empty"===P?null:"blur"===P?'url("data:image/svg+xml;charset=utf-8,'+(0,s.getImageBlurSvg)({widthInt:J,heightInt:L,blurWidth:c,blurHeight:u,blurDataURL:C||"",objectFit:B.objectFit})+'")':'url("'+P+'")',Z=a.includes(B.objectFit)?"fill"===B.objectFit?"100% 100%":"cover":B.objectFit,H=Y?{backgroundSize:Z,backgroundPosition:B.objectPosition||"50% 50%",backgroundRepeat:"no-repeat",backgroundImage:Y}:{},K=function(e){let{config:t,src:r,unoptimized:s,width:i,quality:a,sizes:n,loader:l}=e;if(s)return{src:r,srcSet:void 0,sizes:void 0};let{widths:o,kind:d}=function(e,t,r){let{deviceSizes:s,allSizes:i}=e;if(r){let e=/(^|\s)(1?\d?\d)vw/g,t=[];for(let s;s=e.exec(r);)t.push(parseInt(s[2]));if(t.length){let e=.01*Math.min(...t);return{widths:i.filter(t=>t>=s[0]*e),kind:"w"}}return{widths:i,kind:"w"}}return"number"!=typeof t?{widths:s,kind:"w"}:{widths:[...new Set([t,2*t].map(e=>i.find(t=>t>=e)||i[i.length-1]))],kind:"x"}}(t,i,n),c=o.length-1;return{sizes:n||"w"!==d?n:"100vw",srcSet:o.map((e,s)=>l({config:t,src:r,quality:a,width:e})+" "+("w"===d?e:s+1)+d).join(", "),src:l({config:t,src:r,quality:a,width:o[c]})}}({config:d,src:m,unoptimized:x,width:J,quality:X,sizes:p,loader:W});return{props:{...q,loading:V?"lazy":f,fetchPriority:S,width:J,height:L,decoding:E,className:h,style:{...B,...H},sizes:K.sizes,srcSet:K.srcSet,src:w||K.src},meta:{unoptimized:x,priority:g,placeholder:P,fill:_}}}},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9428:e=>{"use strict";e.exports=require("buffer")},9523:(e,t,r)=>{"use strict";r.d(t,{$:()=>o});var s=r(687);r(3210);var i=r(1391),a=r(4224),n=r(4780);let l=(0,a.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o({className:e,variant:t,size:r,asChild:a=!1,...o}){let d=a?i.DX:"button";return(0,s.jsx)(d,{"data-slot":"button",className:(0,n.cn)(l({variant:t,size:r,className:e})),...o})}},9551:e=>{"use strict";e.exports=require("url")},9603:(e,t,r)=>{let{createProxy:s}=r(9844);e.exports=s("C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\node_modules\\next\\dist\\client\\image-component.js")},9667:(e,t,r)=>{"use strict";r.d(t,{p:()=>a});var s=r(687);r(3210);var i=r(4780);function a({className:e,type:t,...r}){return(0,s.jsx)("input",{type:t,"data-slot":"input",className:(0,i.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...r})}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,455,342,596,23,583,255,576,830],()=>r(4897));module.exports=s})();