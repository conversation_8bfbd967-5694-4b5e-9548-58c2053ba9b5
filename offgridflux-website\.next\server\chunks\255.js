exports.id=255,exports.ids=[255],exports.modules={197:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6444,23)),Promise.resolve().then(r.t.bind(r,6042,23)),Promise.resolve().then(r.t.bind(r,8170,23)),Promise.resolve().then(r.t.bind(r,9477,23)),Promise.resolve().then(r.t.bind(r,9345,23)),Promise.resolve().then(r.t.bind(r,2089,23)),Promise.resolve().then(r.t.bind(r,6577,23)),Promise.resolve().then(r.t.bind(r,1307,23))},225:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GA_MEASUREMENT_ID:()=>o,initGA:()=>n,initScrollTracking:()=>y,initTimeTracking:()=>C,setCustomDimensions:()=>x,trackABTest:()=>k,trackAffiliateClick:()=>a,trackContentEngagement:()=>f,trackConversionFunnel:()=>h,trackEmailInteraction:()=>_,trackError:()=>w,trackEvent:()=>s,trackNewsletterSignup:()=>c,trackPageView:()=>i,trackPerformanceMetric:()=>b,trackProductView:()=>l,trackPurchaseIntent:()=>g,trackScrollDepth:()=>p,trackSearch:()=>d,trackSocialShare:()=>v,trackTimeOnPage:()=>m,trackUserPreference:()=>u});let o="G-XXXXXXXXXX",n=()=>{},i=(e,t)=>{},s=(e,t,r,o,n)=>{},a=(e,t,r,o,n)=>{s("affiliate_click","ecommerce",e,o,{product_id:t,product_name:e,product_brand:r,product_price:o,affiliate_url:n,currency:"USD"})},l=(e,t,r,o,n)=>{},c=e=>{s("newsletter_signup","engagement",e,void 0,{signup_source:e})},d=(e,t)=>{s("search","engagement",e,t,{search_term:e,results_count:t})},f=(e,t,r,o)=>{s(r,"content",t,o,{content_type:e,content_id:t})},p=(e,t)=>{s("scroll_depth","engagement",t,e,{scroll_percentage:e,page_path:t})},m=(e,t)=>{s("time_on_page","engagement",t,e,{time_seconds:e,page_path:t})},g=(e,t,r,o,n)=>{},u=(e,t)=>{s("user_preference","personalization",e,void 0,{preference_type:e,preference_value:t})},h=(e,t,r)=>{s("conversion_funnel","ecommerce",e,void 0,{funnel_stage:e,traffic_source:t,product_id:r})},v=(e,t,r)=>{s("social_share","engagement",e,void 0,{social_platform:e,content_type:t,content_id:r})},_=(e,t,r)=>{s("email_interaction","engagement",e,void 0,{email_action:e,campaign_id:t,email_type:r})},b=(e,t,r)=>{s("performance_metric","technical",e,t,{metric_name:e,metric_value:t,page_path:r})},w=(e,t,r,o="medium")=>{s("error","technical",e,void 0,{error_type:e,error_message:t,error_severity:o,page_path:r})},k=(e,t,r)=>{s("ab_test","optimization",e,void 0,{test_name:e,test_variant:t,test_action:r})},x=e=>{},y=()=>{},C=()=>{}},1135:()=>{},4431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l,metadata:()=>a});var o=r(7413),n=r(5091),i=r.n(n),s=r(7023);r(1135);let a={title:{default:"OffGridFlux - Best Portable Power Stations 2025 | Expert Reviews",template:"%s | OffGridFlux"},description:"Expert reviews and real-world testing of portable power stations from Jackery, Goal Zero, and EcoFlow. Find your perfect off-grid power solution.",keywords:"portable power station, solar generator, off grid power, jackery, goal zero, ecoflow, battery backup, camping power",authors:[{name:"OffGridFlux Team"}],creator:"OffGridFlux",publisher:"OffGridFlux",metadataBase:new URL("https://offgridflux.com"),openGraph:{type:"website",locale:"en_US",url:"https://offgridflux.com",siteName:"OffGridFlux",title:"OffGridFlux - Best Portable Power Stations 2025",description:"Expert reviews and real-world testing of portable power stations. Find your perfect off-grid power solution.",images:[{url:"/og-image.jpg",width:1200,height:630,alt:"OffGridFlux - Portable Power Station Reviews"}]},twitter:{card:"summary_large_image",title:"OffGridFlux - Best Portable Power Stations 2025",description:"Expert reviews and real-world testing of portable power stations.",images:["/og-image.jpg"]},robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}}};function l({children:e}){return(0,o.jsxs)("html",{lang:"en",children:[(0,o.jsx)("head",{children:(0,o.jsx)(s.GoogleAnalytics,{})}),(0,o.jsx)("body",{className:`${i().variable} font-sans antialiased`,children:e})]})}},5713:(e,t,r)=>{Promise.resolve().then(r.bind(r,7023))},5985:(e,t,r)=>{Promise.resolve().then(r.bind(r,7689))},6645:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6346,23)),Promise.resolve().then(r.t.bind(r,7924,23)),Promise.resolve().then(r.t.bind(r,5656,23)),Promise.resolve().then(r.t.bind(r,99,23)),Promise.resolve().then(r.t.bind(r,8243,23)),Promise.resolve().then(r.t.bind(r,8827,23)),Promise.resolve().then(r.t.bind(r,2763,23)),Promise.resolve().then(r.t.bind(r,7173,23))},7023:(e,t,r)=>{"use strict";r.d(t,{GoogleAnalytics:()=>n});var o=r(2907);let n=(0,o.registerClientReference)(function(){throw Error("Attempted to call GoogleAnalytics() from the server but GoogleAnalytics is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\GoogleAnalytics.tsx","GoogleAnalytics");(0,o.registerClientReference)(function(){throw Error("Attempted to call useAffiliateTracking() from the server but useAffiliateTracking is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\GoogleAnalytics.tsx","useAffiliateTracking"),(0,o.registerClientReference)(function(){throw Error("Attempted to call useProductTracking() from the server but useProductTracking is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\GoogleAnalytics.tsx","useProductTracking"),(0,o.registerClientReference)(function(){throw Error("Attempted to call useNewsletterTracking() from the server but useNewsletterTracking is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\GoogleAnalytics.tsx","useNewsletterTracking"),(0,o.registerClientReference)(function(){throw Error("Attempted to call useSearchTracking() from the server but useSearchTracking is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\GoogleAnalytics.tsx","useSearchTracking"),(0,o.registerClientReference)(function(){throw Error("Attempted to call useContentTracking() from the server but useContentTracking is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\GoogleAnalytics.tsx","useContentTracking")},7689:(e,t,r)=>{"use strict";r.d(t,{GoogleAnalytics:()=>s,Sz:()=>a,k8:()=>l});var o=r(687);r(3210);var n=r(6189),i=r(225);function s(){return((0,n.usePathname)(),i.GA_MEASUREMENT_ID&&"G-XXXXXXXXXX"!==i.GA_MEASUREMENT_ID)?(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("script",{async:!0,src:`https://www.googletagmanager.com/gtag/js?id=${i.GA_MEASUREMENT_ID}`}),(0,o.jsx)("script",{dangerouslySetInnerHTML:{__html:`
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', '${i.GA_MEASUREMENT_ID}', {
              page_title: document.title,
              page_location: window.location.href,
            });
          `}})]}):null}function a(){return{trackClick:(e,t,o,n,i)=>{Promise.resolve().then(r.bind(r,225)).then(({trackAffiliateClick:r})=>{r(e,t,o,n,i)})}}}function l(){return{trackView:(e,t,o,n,i)=>{Promise.resolve().then(r.bind(r,225)).then(({trackProductView:r})=>{r(e,t,o,n,i)})}}}}};