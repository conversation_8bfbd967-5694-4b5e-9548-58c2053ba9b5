{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/offgridflux/offgridflux-website/src/lib/analytics.ts"], "sourcesContent": ["// OffGridFlux - Analytics Integration\n// Google Analytics 4 and conversion tracking\n\ndeclare global {\n  interface Window {\n    gtag: (...args: unknown[]) => void;\n    dataLayer: unknown[];\n  }\n}\n\n// Google Analytics 4 Configuration\nexport const GA_MEASUREMENT_ID = process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID || 'G-XXXXXXXXXX';\n\n// Initialize Google Analytics\nexport const initGA = () => {\n  if (typeof window === 'undefined') return;\n  \n  // Load Google Analytics script\n  const script = document.createElement('script');\n  script.src = `https://www.googletagmanager.com/gtag/js?id=${GA_MEASUREMENT_ID}`;\n  script.async = true;\n  document.head.appendChild(script);\n  \n  // Initialize dataLayer and gtag\n  window.dataLayer = window.dataLayer || [];\n  window.gtag = function gtag(...args: unknown[]) {\n    window.dataLayer.push(args);\n  };\n  \n  window.gtag('js', new Date());\n  window.gtag('config', GA_MEASUREMENT_ID, {\n    page_title: document.title,\n    page_location: window.location.href,\n  });\n};\n\n// Track page views\nexport const trackPageView = (url: string, title?: string) => {\n  if (typeof window === 'undefined' || !window.gtag) return;\n  \n  window.gtag('config', GA_MEASUREMENT_ID, {\n    page_title: title || document.title,\n    page_location: url,\n  });\n};\n\n// Track custom events\nexport const trackEvent = (\n  action: string,\n  category: string,\n  label?: string,\n  value?: number,\n  customParameters?: Record<string, unknown>\n) => {\n  if (typeof window === 'undefined' || !window.gtag) return;\n  \n  window.gtag('event', action, {\n    event_category: category,\n    event_label: label,\n    value: value,\n    ...customParameters,\n  });\n};\n\n// E-commerce and affiliate tracking\nexport const trackAffiliateClick = (\n  productName: string,\n  productId: string,\n  brand: string,\n  price: number,\n  url: string\n) => {\n  trackEvent('affiliate_click', 'ecommerce', productName, price, {\n    product_id: productId,\n    product_name: productName,\n    product_brand: brand,\n    product_price: price,\n    affiliate_url: url,\n    currency: 'USD',\n  });\n};\n\n// Track product views\nexport const trackProductView = (\n  productName: string,\n  productId: string,\n  brand: string,\n  price: number,\n  category: string\n) => {\n  if (typeof window === 'undefined' || !window.gtag) return;\n  \n  window.gtag('event', 'view_item', {\n    currency: 'USD',\n    value: price,\n    items: [\n      {\n        item_id: productId,\n        item_name: productName,\n        item_brand: brand,\n        item_category: category,\n        price: price,\n        quantity: 1,\n      },\n    ],\n  });\n};\n\n// Track newsletter signups\nexport const trackNewsletterSignup = (source: string) => {\n  trackEvent('newsletter_signup', 'engagement', source, undefined, {\n    signup_source: source,\n  });\n};\n\n// Track search queries\nexport const trackSearch = (searchTerm: string, resultsCount: number) => {\n  trackEvent('search', 'engagement', searchTerm, resultsCount, {\n    search_term: searchTerm,\n    results_count: resultsCount,\n  });\n};\n\n// Track content engagement\nexport const trackContentEngagement = (\n  contentType: string,\n  contentId: string,\n  engagementType: string,\n  value?: number\n) => {\n  trackEvent(engagementType, 'content', contentId, value, {\n    content_type: contentType,\n    content_id: contentId,\n  });\n};\n\n// Track scroll depth\nexport const trackScrollDepth = (percentage: number, page: string) => {\n  trackEvent('scroll_depth', 'engagement', page, percentage, {\n    scroll_percentage: percentage,\n    page_path: page,\n  });\n};\n\n// Track time on page\nexport const trackTimeOnPage = (timeInSeconds: number, page: string) => {\n  trackEvent('time_on_page', 'engagement', page, timeInSeconds, {\n    time_seconds: timeInSeconds,\n    page_path: page,\n  });\n};\n\n// Enhanced E-commerce tracking for affiliate conversions\nexport const trackPurchaseIntent = (\n  productName: string,\n  productId: string,\n  brand: string,\n  price: number,\n  step: 'view_review' | 'click_affiliate' | 'add_to_cart'\n) => {\n  const eventName = step === 'view_review' ? 'view_item' : \n                   step === 'click_affiliate' ? 'select_item' : 'add_to_cart';\n  \n  if (typeof window === 'undefined' || !window.gtag) return;\n  \n  window.gtag('event', eventName, {\n    currency: 'USD',\n    value: price,\n    items: [\n      {\n        item_id: productId,\n        item_name: productName,\n        item_brand: brand,\n        item_category: 'Portable Power Station',\n        price: price,\n        quantity: 1,\n      },\n    ],\n  });\n};\n\n// Track user preferences and behavior\nexport const trackUserPreference = (\n  preferenceType: string,\n  preferenceValue: string\n) => {\n  trackEvent('user_preference', 'personalization', preferenceType, undefined, {\n    preference_type: preferenceType,\n    preference_value: preferenceValue,\n  });\n};\n\n// Conversion tracking for different funnel stages\nexport const trackConversionFunnel = (\n  stage: 'awareness' | 'consideration' | 'decision' | 'action',\n  source: string,\n  productId?: string\n) => {\n  trackEvent('conversion_funnel', 'ecommerce', stage, undefined, {\n    funnel_stage: stage,\n    traffic_source: source,\n    product_id: productId,\n  });\n};\n\n// Track social shares\nexport const trackSocialShare = (\n  platform: string,\n  contentType: string,\n  contentId: string\n) => {\n  trackEvent('social_share', 'engagement', platform, undefined, {\n    social_platform: platform,\n    content_type: contentType,\n    content_id: contentId,\n  });\n};\n\n// Track email interactions\nexport const trackEmailInteraction = (\n  action: 'open' | 'click' | 'unsubscribe',\n  campaignId: string,\n  emailType: string\n) => {\n  trackEvent('email_interaction', 'engagement', action, undefined, {\n    email_action: action,\n    campaign_id: campaignId,\n    email_type: emailType,\n  });\n};\n\n// Performance tracking\nexport const trackPerformanceMetric = (\n  metricName: string,\n  value: number,\n  page: string\n) => {\n  trackEvent('performance_metric', 'technical', metricName, value, {\n    metric_name: metricName,\n    metric_value: value,\n    page_path: page,\n  });\n};\n\n// Error tracking\nexport const trackError = (\n  errorType: string,\n  errorMessage: string,\n  page: string,\n  severity: 'low' | 'medium' | 'high' = 'medium'\n) => {\n  trackEvent('error', 'technical', errorType, undefined, {\n    error_type: errorType,\n    error_message: errorMessage,\n    error_severity: severity,\n    page_path: page,\n  });\n};\n\n// A/B testing tracking\nexport const trackABTest = (\n  testName: string,\n  variant: string,\n  action: 'view' | 'convert'\n) => {\n  trackEvent('ab_test', 'optimization', testName, undefined, {\n    test_name: testName,\n    test_variant: variant,\n    test_action: action,\n  });\n};\n\n// Custom dimensions for enhanced tracking\nexport const setCustomDimensions = (dimensions: Record<string, string>) => {\n  if (typeof window === 'undefined' || !window.gtag) return;\n  \n  window.gtag('config', GA_MEASUREMENT_ID, {\n    custom_map: dimensions,\n  });\n};\n\n// Initialize scroll depth tracking\nexport const initScrollTracking = () => {\n  if (typeof window === 'undefined') return;\n  \n  let maxScroll = 0;\n  const thresholds = [25, 50, 75, 90, 100];\n  const tracked = new Set<number>();\n  \n  const handleScroll = () => {\n    const scrollPercent = Math.round(\n      (window.scrollY / (document.documentElement.scrollHeight - window.innerHeight)) * 100\n    );\n    \n    if (scrollPercent > maxScroll) {\n      maxScroll = scrollPercent;\n      \n      thresholds.forEach(threshold => {\n        if (scrollPercent >= threshold && !tracked.has(threshold)) {\n          tracked.add(threshold);\n          trackScrollDepth(threshold, window.location.pathname);\n        }\n      });\n    }\n  };\n  \n  window.addEventListener('scroll', handleScroll, { passive: true });\n  \n  return () => {\n    window.removeEventListener('scroll', handleScroll);\n  };\n};\n\n// Initialize time tracking\nexport const initTimeTracking = () => {\n  if (typeof window === 'undefined') return;\n  \n  const startTime = Date.now();\n  \n  const trackTime = () => {\n    const timeSpent = Math.round((Date.now() - startTime) / 1000);\n    trackTimeOnPage(timeSpent, window.location.pathname);\n  };\n  \n  // Track time on page visibility change\n  document.addEventListener('visibilitychange', () => {\n    if (document.visibilityState === 'hidden') {\n      trackTime();\n    }\n  });\n  \n  // Track time on page unload\n  window.addEventListener('beforeunload', trackTime);\n  \n  return trackTime;\n};\n"], "names": [], "mappings": "AAAA,sCAAsC;AACtC,6CAA6C;;;;;;;;;;;;;;;;;;;;;;;;;AAUtC,MAAM,oBAAoB,wCAA6C;AAGvE,MAAM,SAAS;IACpB,wCAAmC;;IAEnC,+BAA+B;IAC/B,MAAM;AAgBR;AAGO,MAAM,gBAAgB,CAAC,KAAa;IACzC,wCAAmD;;AAMrD;AAGO,MAAM,aAAa,CACxB,QACA,UACA,OACA,OACA;IAEA,wCAAmD;;AAQrD;AAGO,MAAM,sBAAsB,CACjC,aACA,WACA,OACA,OACA;IAEA,WAAW,mBAAmB,aAAa,aAAa,OAAO;QAC7D,YAAY;QACZ,cAAc;QACd,eAAe;QACf,eAAe;QACf,eAAe;QACf,UAAU;IACZ;AACF;AAGO,MAAM,mBAAmB,CAC9B,aACA,WACA,OACA,OACA;IAEA,wCAAmD;;AAgBrD;AAGO,MAAM,wBAAwB,CAAC;IACpC,WAAW,qBAAqB,cAAc,QAAQ,WAAW;QAC/D,eAAe;IACjB;AACF;AAGO,MAAM,cAAc,CAAC,YAAoB;IAC9C,WAAW,UAAU,cAAc,YAAY,cAAc;QAC3D,aAAa;QACb,eAAe;IACjB;AACF;AAGO,MAAM,yBAAyB,CACpC,aACA,WACA,gBACA;IAEA,WAAW,gBAAgB,WAAW,WAAW,OAAO;QACtD,cAAc;QACd,YAAY;IACd;AACF;AAGO,MAAM,mBAAmB,CAAC,YAAoB;IACnD,WAAW,gBAAgB,cAAc,MAAM,YAAY;QACzD,mBAAmB;QACnB,WAAW;IACb;AACF;AAGO,MAAM,kBAAkB,CAAC,eAAuB;IACrD,WAAW,gBAAgB,cAAc,MAAM,eAAe;QAC5D,cAAc;QACd,WAAW;IACb;AACF;AAGO,MAAM,sBAAsB,CACjC,aACA,WACA,OACA,OACA;IAEA,MAAM,YAAY,SAAS,gBAAgB,cAC1B,SAAS,oBAAoB,gBAAgB;IAE9D,wCAAmD;;AAgBrD;AAGO,MAAM,sBAAsB,CACjC,gBACA;IAEA,WAAW,mBAAmB,mBAAmB,gBAAgB,WAAW;QAC1E,iBAAiB;QACjB,kBAAkB;IACpB;AACF;AAGO,MAAM,wBAAwB,CACnC,OACA,QACA;IAEA,WAAW,qBAAqB,aAAa,OAAO,WAAW;QAC7D,cAAc;QACd,gBAAgB;QAChB,YAAY;IACd;AACF;AAGO,MAAM,mBAAmB,CAC9B,UACA,aACA;IAEA,WAAW,gBAAgB,cAAc,UAAU,WAAW;QAC5D,iBAAiB;QACjB,cAAc;QACd,YAAY;IACd;AACF;AAGO,MAAM,wBAAwB,CACnC,QACA,YACA;IAEA,WAAW,qBAAqB,cAAc,QAAQ,WAAW;QAC/D,cAAc;QACd,aAAa;QACb,YAAY;IACd;AACF;AAGO,MAAM,yBAAyB,CACpC,YACA,OACA;IAEA,WAAW,sBAAsB,aAAa,YAAY,OAAO;QAC/D,aAAa;QACb,cAAc;QACd,WAAW;IACb;AACF;AAGO,MAAM,aAAa,CACxB,WACA,cACA,MACA,WAAsC,QAAQ;IAE9C,WAAW,SAAS,aAAa,WAAW,WAAW;QACrD,YAAY;QACZ,eAAe;QACf,gBAAgB;QAChB,WAAW;IACb;AACF;AAGO,MAAM,cAAc,CACzB,UACA,SACA;IAEA,WAAW,WAAW,gBAAgB,UAAU,WAAW;QACzD,WAAW;QACX,cAAc;QACd,aAAa;IACf;AACF;AAGO,MAAM,sBAAsB,CAAC;IAClC,wCAAmD;;AAKrD;AAGO,MAAM,qBAAqB;IAChC,wCAAmC;;IAEnC,IAAI;IACJ,MAAM;IACN,MAAM;IAEN,MAAM;AAsBR;AAGO,MAAM,mBAAmB;IAC9B,wCAAmC;;IAEnC,MAAM;IAEN,MAAM;AAgBR", "debugId": null}}, {"offset": {"line": 201, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/offgridflux/offgridflux-website/src/components/GoogleAnalytics.tsx"], "sourcesContent": ["// OffGridFlux - Google Analytics Component\n// Client-side analytics initialization and tracking\n\n'use client';\n\nimport { useEffect } from 'react';\nimport { usePathname } from 'next/navigation';\nimport {\n  initGA,\n  trackPageView,\n  initScrollTracking,\n  initTimeTracking,\n  GA_MEASUREMENT_ID\n} from '@/lib/analytics';\n\nexport function GoogleAnalytics() {\n  const pathname = usePathname();\n\n  useEffect(() => {\n    // Initialize Google Analytics\n    initGA();\n\n    // Initialize scroll and time tracking\n    const cleanupScroll = initScrollTracking();\n    const cleanupTime = initTimeTracking();\n\n    return () => {\n      if (cleanupScroll) cleanupScroll();\n      if (cleanupTime) cleanupTime();\n    };\n  }, []);\n\n  useEffect(() => {\n    // Track page views on route changes\n    trackPageView(pathname);\n  }, [pathname]);\n\n  // Only render in production with valid measurement ID\n  if (process.env.NODE_ENV !== 'production' || !GA_MEASUREMENT_ID || GA_MEASUREMENT_ID === 'G-XXXXXXXXXX') {\n    return null;\n  }\n\n  return (\n    <>\n      {/* Google Analytics Script */}\n      <script\n        async\n        src={`https://www.googletagmanager.com/gtag/js?id=${GA_MEASUREMENT_ID}`}\n      />\n      <script\n        dangerouslySetInnerHTML={{\n          __html: `\n            window.dataLayer = window.dataLayer || [];\n            function gtag(){dataLayer.push(arguments);}\n            gtag('js', new Date());\n            gtag('config', '${GA_MEASUREMENT_ID}', {\n              page_title: document.title,\n              page_location: window.location.href,\n            });\n          `,\n        }}\n      />\n    </>\n  );\n}\n\n// Hook for tracking affiliate clicks\nexport function useAffiliateTracking() {\n  const trackClick = (\n    productName: string,\n    productId: string,\n    brand: string,\n    price: number,\n    url: string\n  ) => {\n    // Import analytics functions dynamically to avoid SSR issues\n    import('@/lib/analytics').then(({ trackAffiliateClick }) => {\n      trackAffiliateClick(productName, productId, brand, price, url);\n    });\n  };\n\n  return { trackClick };\n}\n\n// Hook for tracking product views\nexport function useProductTracking() {\n  const trackView = (\n    productName: string,\n    productId: string,\n    brand: string,\n    price: number,\n    category: string\n  ) => {\n    import('@/lib/analytics').then(({ trackProductView }) => {\n      trackProductView(productName, productId, brand, price, category);\n    });\n  };\n\n  return { trackView };\n}\n\n// Hook for tracking newsletter signups\nexport function useNewsletterTracking() {\n  const trackSignup = (source: string) => {\n    import('@/lib/analytics').then(({ trackNewsletterSignup }) => {\n      trackNewsletterSignup(source);\n    });\n  };\n\n  return { trackSignup };\n}\n\n// Hook for tracking search\nexport function useSearchTracking() {\n  const trackSearch = (searchTerm: string, resultsCount: number) => {\n    import('@/lib/analytics').then(({ trackSearch }) => {\n      trackSearch(searchTerm, resultsCount);\n    });\n  };\n\n  return { trackSearch };\n}\n\n// Hook for tracking content engagement\nexport function useContentTracking() {\n  const trackEngagement = (\n    contentType: string,\n    contentId: string,\n    engagementType: string,\n    value?: number\n  ) => {\n    import('@/lib/analytics').then(({ trackContentEngagement }) => {\n      trackContentEngagement(contentType, contentId, engagementType, value);\n    });\n  };\n\n  return { trackEngagement };\n}\n"], "names": [], "mappings": "AAAA,2CAA2C;AAC3C,oDAAoD;;;;;;;;;;AAIpD;AACA;AACA;AAJA;;;;;AAYO,SAAS;IACd,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,8BAA8B;QAC9B,CAAA,GAAA,uHAAA,CAAA,SAAM,AAAD;QAEL,sCAAsC;QACtC,MAAM,gBAAgB,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD;QACvC,MAAM,cAAc,CAAA,GAAA,uHAAA,CAAA,mBAAgB,AAAD;QAEnC,OAAO;YACL,IAAI,eAAe;YACnB,IAAI,aAAa;QACnB;IACF,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,oCAAoC;QACpC,CAAA,GAAA,uHAAA,CAAA,gBAAa,AAAD,EAAE;IAChB,GAAG;QAAC;KAAS;IAEb,sDAAsD;IACtD,wCAAyG;QACvG,OAAO;IACT;;AAwBF;AAGO,SAAS;IACd,MAAM,aAAa,CACjB,aACA,WACA,OACA,OACA;QAEA,6DAA6D;QAC7D,wHAA0B,IAAI,CAAC,CAAC,EAAE,mBAAmB,EAAE;YACrD,oBAAoB,aAAa,WAAW,OAAO,OAAO;QAC5D;IACF;IAEA,OAAO;QAAE;IAAW;AACtB;AAGO,SAAS;IACd,MAAM,YAAY,CAChB,aACA,WACA,OACA,OACA;QAEA,wHAA0B,IAAI,CAAC,CAAC,EAAE,gBAAgB,EAAE;YAClD,iBAAiB,aAAa,WAAW,OAAO,OAAO;QACzD;IACF;IAEA,OAAO;QAAE;IAAU;AACrB;AAGO,SAAS;IACd,MAAM,cAAc,CAAC;QACnB,wHAA0B,IAAI,CAAC,CAAC,EAAE,qBAAqB,EAAE;YACvD,sBAAsB;QACxB;IACF;IAEA,OAAO;QAAE;IAAY;AACvB;AAGO,SAAS;IACd,MAAM,cAAc,CAAC,YAAoB;QACvC,wHAA0B,IAAI,CAAC,CAAC,EAAE,WAAW,EAAE;YAC7C,YAAY,YAAY;QAC1B;IACF;IAEA,OAAO;QAAE;IAAY;AACvB;AAGO,SAAS;IACd,MAAM,kBAAkB,CACtB,aACA,WACA,gBACA;QAEA,wHAA0B,IAAI,CAAC,CAAC,EAAE,sBAAsB,EAAE;YACxD,uBAAuB,aAAa,WAAW,gBAAgB;QACjE;IACF;IAEA,OAAO;QAAE;IAAgB;AAC3B", "debugId": null}}, {"offset": {"line": 302, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/offgridflux/offgridflux-website/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 318, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/offgridflux/offgridflux-website/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 375, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/offgridflux/offgridflux-website/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 421, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/offgridflux/offgridflux-website/src/components/Header.tsx"], "sourcesContent": ["// OffGridFlux - Header Navigation Component\n// Main site navigation with responsive design\n\n'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\nimport { Button } from '@/components/ui/button';\nimport { Badge } from '@/components/ui/badge';\nimport { Menu, X, Zap, ChevronDown, Search } from 'lucide-react';\n\nconst navigation = [\n  {\n    name: 'Reviews',\n    href: '/reviews',\n    description: 'In-depth product reviews'\n  },\n  {\n    name: 'Categories',\n    href: '#',\n    description: 'Browse by product type',\n    children: [\n      { name: 'Portable Power Stations', href: '/category/portable-power-stations' },\n      { name: 'Solar Generators', href: '/category/solar-generators' },\n      { name: 'Home Battery Systems', href: '/category/home-battery-systems' }\n    ]\n  },\n  {\n    name: 'Guides',\n    href: '/guides',\n    description: 'Buying guides and tutorials'\n  },\n  {\n    name: 'Best Of 2025',\n    href: '/best-portable-power-stations-2025',\n    badge: 'Popular'\n  },\n  {\n    name: 'About',\n    href: '/about'\n  }\n];\n\nexport function Header() {\n  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);\n  const [dropdownOpen, setDropdownOpen] = useState<string | null>(null);\n\n  return (\n    <header className=\"sticky top-0 z-50 bg-white/95 backdrop-blur-sm border-b border-gray-200\">\n      <nav className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex h-16 items-center justify-between\">\n          \n          {/* Logo */}\n          <div className=\"flex items-center\">\n            <Link href=\"/\" className=\"flex items-center space-x-2\">\n              <div className=\"flex items-center justify-center w-8 h-8 bg-primary-600 rounded-lg\">\n                <Zap className=\"w-5 h-5 text-white\" />\n              </div>\n              <span className=\"text-xl font-bold text-gray-900\">OffGridFlux</span>\n            </Link>\n          </div>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:flex items-center space-x-8\">\n            {navigation.map((item) => (\n              <div key={item.name} className=\"relative\">\n                {item.children ? (\n                  <div\n                    className=\"relative\"\n                    onMouseEnter={() => setDropdownOpen(item.name)}\n                    onMouseLeave={() => setDropdownOpen(null)}\n                  >\n                    <button className=\"flex items-center text-gray-700 hover:text-primary-600 font-medium transition-colors\">\n                      {item.name}\n                      <ChevronDown className=\"ml-1 w-4 h-4\" />\n                    </button>\n                    \n                    {dropdownOpen === item.name && (\n                      <div className=\"absolute top-full left-0 mt-2 w-64 bg-white rounded-lg shadow-lg border border-gray-200 py-2\">\n                        {item.children.map((child) => (\n                          <Link\n                            key={child.name}\n                            href={child.href}\n                            className=\"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 hover:text-primary-600\"\n                          >\n                            {child.name}\n                          </Link>\n                        ))}\n                      </div>\n                    )}\n                  </div>\n                ) : (\n                  <Link\n                    href={item.href}\n                    className=\"flex items-center text-gray-700 hover:text-primary-600 font-medium transition-colors\"\n                  >\n                    {item.name}\n                    {item.badge && (\n                      <Badge className=\"ml-2 bg-warning-100 text-warning-700 text-xs\">\n                        {item.badge}\n                      </Badge>\n                    )}\n                  </Link>\n                )}\n              </div>\n            ))}\n          </div>\n\n          {/* Desktop CTA */}\n          <div className=\"hidden md:flex items-center space-x-4\">\n            <Button variant=\"outline\" size=\"sm\" className=\"flex items-center\">\n              <Search className=\"w-4 h-4 mr-2\" />\n              Search\n            </Button>\n            <Button size=\"sm\" className=\"bg-primary-600 hover:bg-primary-700\">\n              Newsletter\n            </Button>\n          </div>\n\n          {/* Mobile menu button */}\n          <div className=\"md:hidden\">\n            <button\n              type=\"button\"\n              className=\"text-gray-700 hover:text-primary-600\"\n              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}\n            >\n              {mobileMenuOpen ? (\n                <X className=\"w-6 h-6\" />\n              ) : (\n                <Menu className=\"w-6 h-6\" />\n              )}\n            </button>\n          </div>\n        </div>\n\n        {/* Mobile Navigation */}\n        {mobileMenuOpen && (\n          <div className=\"md:hidden border-t border-gray-200 py-4\">\n            <div className=\"space-y-4\">\n              {navigation.map((item) => (\n                <div key={item.name}>\n                  {item.children ? (\n                    <div>\n                      <div className=\"text-gray-900 font-medium py-2\">\n                        {item.name}\n                      </div>\n                      <div className=\"pl-4 space-y-2\">\n                        {item.children.map((child) => (\n                          <Link\n                            key={child.name}\n                            href={child.href}\n                            className=\"block text-gray-600 hover:text-primary-600 py-1\"\n                            onClick={() => setMobileMenuOpen(false)}\n                          >\n                            {child.name}\n                          </Link>\n                        ))}\n                      </div>\n                    </div>\n                  ) : (\n                    <Link\n                      href={item.href}\n                      className=\"flex items-center text-gray-700 hover:text-primary-600 font-medium py-2\"\n                      onClick={() => setMobileMenuOpen(false)}\n                    >\n                      {item.name}\n                      {item.badge && (\n                        <Badge className=\"ml-2 bg-warning-100 text-warning-700 text-xs\">\n                          {item.badge}\n                        </Badge>\n                      )}\n                    </Link>\n                  )}\n                </div>\n              ))}\n              \n              <div className=\"pt-4 border-t border-gray-200 space-y-2\">\n                <Button variant=\"outline\" size=\"sm\" className=\"w-full justify-center\">\n                  <Search className=\"w-4 h-4 mr-2\" />\n                  Search\n                </Button>\n                <Button size=\"sm\" className=\"w-full bg-primary-600 hover:bg-primary-700\">\n                  Newsletter\n                </Button>\n              </div>\n            </div>\n          </div>\n        )}\n      </nav>\n    </header>\n  );\n}\n"], "names": [], "mappings": "AAAA,4CAA4C;AAC5C,8CAA8C;;;;;AAI9C;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AANA;;;;;;;AAQA,MAAM,aAAa;IACjB;QACE,MAAM;QACN,MAAM;QACN,aAAa;IACf;IACA;QACE,MAAM;QACN,MAAM;QACN,aAAa;QACb,UAAU;YACR;gBAAE,MAAM;gBAA2B,MAAM;YAAoC;YAC7E;gBAAE,MAAM;gBAAoB,MAAM;YAA6B;YAC/D;gBAAE,MAAM;gBAAwB,MAAM;YAAiC;SACxE;IACH;IACA;QACE,MAAM;QACN,MAAM;QACN,aAAa;IACf;IACA;QACE,MAAM;QACN,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;IACR;CACD;AAEM,SAAS;IACd,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAEhE,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAGb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;;kDACvB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,gMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;;;;;;kDAEjB,8OAAC;wCAAK,WAAU;kDAAkC;;;;;;;;;;;;;;;;;sCAKtD,8OAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC;oCAAoB,WAAU;8CAC5B,KAAK,QAAQ,iBACZ,8OAAC;wCACC,WAAU;wCACV,cAAc,IAAM,gBAAgB,KAAK,IAAI;wCAC7C,cAAc,IAAM,gBAAgB;;0DAEpC,8OAAC;gDAAO,WAAU;;oDACf,KAAK,IAAI;kEACV,8OAAC,oNAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;;;;;;;4CAGxB,iBAAiB,KAAK,IAAI,kBACzB,8OAAC;gDAAI,WAAU;0DACZ,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,sBAClB,8OAAC,4JAAA,CAAA,UAAI;wDAEH,MAAM,MAAM,IAAI;wDAChB,WAAU;kEAET,MAAM,IAAI;uDAJN,MAAM,IAAI;;;;;;;;;;;;;;;6DAWzB,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAM,KAAK,IAAI;wCACf,WAAU;;4CAET,KAAK,IAAI;4CACT,KAAK,KAAK,kBACT,8OAAC,iIAAA,CAAA,QAAK;gDAAC,WAAU;0DACd,KAAK,KAAK;;;;;;;;;;;;mCAlCX,KAAK,IAAI;;;;;;;;;;sCA4CvB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,MAAK;oCAAK,WAAU;;sDAC5C,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGrC,8OAAC,kIAAA,CAAA,SAAM;oCAAC,MAAK;oCAAK,WAAU;8CAAsC;;;;;;;;;;;;sCAMpE,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,kBAAkB,CAAC;0CAEjC,+BACC,8OAAC,4LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;yDAEb,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;gBAOvB,gCACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;4BACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC;8CACE,KAAK,QAAQ,iBACZ,8OAAC;;0DACC,8OAAC;gDAAI,WAAU;0DACZ,KAAK,IAAI;;;;;;0DAEZ,8OAAC;gDAAI,WAAU;0DACZ,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,sBAClB,8OAAC,4JAAA,CAAA,UAAI;wDAEH,MAAM,MAAM,IAAI;wDAChB,WAAU;wDACV,SAAS,IAAM,kBAAkB;kEAEhC,MAAM,IAAI;uDALN,MAAM,IAAI;;;;;;;;;;;;;;;6DAWvB,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAM,KAAK,IAAI;wCACf,WAAU;wCACV,SAAS,IAAM,kBAAkB;;4CAEhC,KAAK,IAAI;4CACT,KAAK,KAAK,kBACT,8OAAC,iIAAA,CAAA,QAAK;gDAAC,WAAU;0DACd,KAAK,KAAK;;;;;;;;;;;;mCA5BX,KAAK,IAAI;;;;;0CAoCrB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;wCAAK,WAAU;;0DAC5C,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGrC,8OAAC,kIAAA,CAAA,SAAM;wCAAC,MAAK;wCAAK,WAAU;kDAA6C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUzF", "debugId": null}}, {"offset": {"line": 810, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/offgridflux/offgridflux-website/src/components/AffiliateDisclosure.tsx"], "sourcesContent": ["// OffGridFlux - Affiliate Disclosure Component\n// FTC-compliant affiliate disclosure banner\n\n'use client';\n\nimport { useState } from 'react';\nimport { Badge } from '@/components/ui/badge';\nimport { Button } from '@/components/ui/button';\nimport { Info, X, ExternalLink } from 'lucide-react';\n\nexport function AffiliateDisclosure() {\n  const [isVisible, setIsVisible] = useState(true);\n  const [isExpanded, setIsExpanded] = useState(false);\n\n  if (!isVisible) return null;\n\n  return (\n    <div className=\"bg-primary-50 border-b border-primary-200 relative\">\n      <div className=\"max-w-7xl mx-auto px-4 py-3\">\n        <div className=\"flex items-start gap-3\">\n          {/* Icon */}\n          <div className=\"flex-shrink-0 mt-0.5\">\n            <Info className=\"w-5 h-5 text-primary-600\" />\n          </div>\n\n          {/* Content */}\n          <div className=\"flex-1 min-w-0\">\n            <div className=\"flex items-center gap-2 mb-1\">\n              <Badge variant=\"outline\" className=\"text-xs border-primary-300 text-primary-700\">\n                FTC Disclosure\n              </Badge>\n            </div>\n\n            <div className=\"text-sm text-primary-800\">\n              <p className=\"font-medium mb-1\">\n                Affiliate Disclosure: We earn commissions from qualifying purchases.\n              </p>\n              \n              {!isExpanded ? (\n                <p className=\"text-primary-700\">\n                  As an Amazon Associate and affiliate partner, we earn from qualifying purchases.\n                  This doesn&apos;t affect our review process or recommendations.{' '}\n                  <button\n                    onClick={() => setIsExpanded(true)}\n                    className=\"underline hover:no-underline font-medium\"\n                  >\n                    Read full disclosure\n                  </button>\n                </p>\n              ) : (\n                <div className=\"text-primary-700 space-y-2\">\n                  <p>\n                    OffGridFlux participates in affiliate programs with Amazon, Jackery, Goal Zero, EcoFlow, and other retailers. \n                    When you click on links to products and make a purchase, we may receive a commission at no additional cost to you.\n                  </p>\n                  <p>\n                    <strong>Our Promise:</strong> We only recommend products we&apos;ve personally tested or would use ourselves.\n                    Our reviews are based on real-world testing and are not influenced by affiliate relationships. \n                    We purchase most products at full retail price for testing.\n                  </p>\n                  <p>\n                    Commission earnings help support our independent testing lab and keep our content free for readers. \n                    For complete details, see our{' '}\n                    <a \n                      href=\"/affiliate-disclosure\" \n                      className=\"underline hover:no-underline font-medium inline-flex items-center gap-1\"\n                    >\n                      full affiliate disclosure policy\n                      <ExternalLink className=\"w-3 h-3\" />\n                    </a>\n                    .\n                  </p>\n                  \n                  <button \n                    onClick={() => setIsExpanded(false)}\n                    className=\"underline hover:no-underline font-medium text-xs\"\n                  >\n                    Show less\n                  </button>\n                </div>\n              )}\n            </div>\n          </div>\n          \n          {/* Close Button */}\n          <Button\n            variant=\"ghost\"\n            size=\"sm\"\n            onClick={() => setIsVisible(false)}\n            className=\"flex-shrink-0 h-8 w-8 p-0 text-primary-600 hover:bg-primary-100\"\n            aria-label=\"Close disclosure\"\n          >\n            <X className=\"w-4 h-4\" />\n          </Button>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA,+CAA+C;AAC/C,4CAA4C;;;;;AAI5C;AACA;AACA;AACA;AAAA;AAAA;AALA;;;;;;AAOO,SAAS;IACd,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,IAAI,CAAC,WAAW,OAAO;IAEvB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;;;;;;kCAIlB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAU,WAAU;8CAA8C;;;;;;;;;;;0CAKnF,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;kDAAmB;;;;;;oCAI/B,CAAC,2BACA,8OAAC;wCAAE,WAAU;;4CAAmB;4CAEkC;0DAChE,8OAAC;gDACC,SAAS,IAAM,cAAc;gDAC7B,WAAU;0DACX;;;;;;;;;;;6DAKH,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;0DAAE;;;;;;0DAIH,8OAAC;;kEACC,8OAAC;kEAAO;;;;;;oDAAqB;;;;;;;0DAI/B,8OAAC;;oDAAE;oDAE6B;kEAC9B,8OAAC;wDACC,MAAK;wDACL,WAAU;;4DACX;0EAEC,8OAAC,sNAAA,CAAA,eAAY;gEAAC,WAAU;;;;;;;;;;;;oDACtB;;;;;;;0DAIN,8OAAC;gDACC,SAAS,IAAM,cAAc;gDAC7B,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;;;kCAST,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS,IAAM,aAAa;wBAC5B,WAAU;wBACV,cAAW;kCAEX,cAAA,8OAAC,4LAAA,CAAA,IAAC;4BAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMzB", "debugId": null}}]}