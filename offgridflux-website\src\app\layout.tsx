import type { Metadata } from "next";
import { Inter } from "next/font/google";
import { GoogleAnalytics } from "@/components/GoogleAnalytics";
import "./globals.css";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
});

export const metadata: Metadata = {
  title: {
    default: "OffGridFlux - Best Portable Power Stations 2025 | Expert Reviews",
    template: "%s | OffGridFlux"
  },
  description: "Expert reviews and real-world testing of portable power stations from Jackery, Goal Zero, and EcoFlow. Find your perfect off-grid power solution.",
  keywords: "portable power station, solar generator, off grid power, jackery, goal zero, ecoflow, battery backup, camping power",
  authors: [{ name: "OffGridFlux Team" }],
  creator: "OffGridFlux",
  publisher: "OffGridFlux",
  metadataBase: new URL("https://offgridflux.com"),
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "https://offgridflux.com",
    siteName: "OffGridFlux",
    title: "OffGridFlux - Best Portable Power Stations 2025",
    description: "Expert reviews and real-world testing of portable power stations. Find your perfect off-grid power solution.",
    images: [
      {
        url: "/og-image.jpg",
        width: 1200,
        height: 630,
        alt: "OffGridFlux - Portable Power Station Reviews"
      }
    ]
  },
  twitter: {
    card: "summary_large_image",
    title: "OffGridFlux - Best Portable Power Stations 2025",
    description: "Expert reviews and real-world testing of portable power stations.",
    images: ["/og-image.jpg"]
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1
    }
  }
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head>
        <GoogleAnalytics />
      </head>
      <body className={`${inter.variable} font-sans antialiased`}>
        {children}
      </body>
    </html>
  );
}
