diff --git a/offgridflux-website/src/components/ProductCard.tsx b/offgridflux-website/src/components/ProductCard.tsx
index 1234567..abcdefg 100644
--- a/offgridflux-website/src/components/ProductCard.tsx
+++ b/offgridflux-website/src/components/ProductCard.tsx
@@ -61,7 +61,7 @@ export function ProductCard({ product }: ProductCardProps) {
         
         {/* Discount Badge */}
         {product.discount_percent && (
-          <div className="absolute top-2 right-2 bg-red-500 text-white text-sm font-medium px-2 py-1 rounded">
+          <div className="absolute top-2 right-2 bg-warning-500 text-white text-sm font-medium px-2 py-1 rounded">
             {product.discount_percent}% OFF
           </div>
         )}
@@ -116,7 +116,7 @@ export function ProductCard({ product }: ProductCardProps) {
                 <Star
                   key={i}
                   className={`w-4 h-4 ${
-                    i < Math.floor(product.offgridflux_rating!)
-                      ? 'text-yellow-400 fill-current'
-                      : 'text-gray-300'
+                    i < Math.floor(product.offgridflux_rating!) 
+                      ? 'text-warning-400 fill-current'
+                      : 'text-neutral-300'
                   }`}
                 />
               ))}
@@ -159,7 +159,7 @@ export function ProductCard({ product }: ProductCardProps) {
             {valueScore && (
               <div className="text-right">
                 <div className="text-xs text-gray-500">Value Score</div>
-                <div className="text-sm font-medium text-green-600">
+                <div className="text-sm font-medium text-energy-600">
                   {valueScore} Wh/$
                 </div>
               </div>
@@ -177,8 +177,9 @@ export function ProductCard({ product }: ProductCardProps) {
               <a
                 href={product.affiliate_url}
                 target="_blank"
                 rel="noopener noreferrer"
                 onClick={handleAffiliateClick}
-                className="block w-full bg-gray-100 hover:bg-gray-200 text-gray-700 text-center py-2 px-4 rounded-lg transition-colors duration-200 text-sm"
+                aria-label={`Check price for ${product.product_name} on retailer site`}
+                className="block w-full bg-neutral-100 hover:bg-neutral-200 text-neutral-700 text-center py-2 px-4 rounded-lg transition-colors duration-200 text-sm"
               >
                 Check Price
               </a>

diff --git a/offgridflux-website/src/components/Hero.tsx b/offgridflux-website/src/components/Hero.tsx
index 2345678..bcdefgh 100644
--- a/offgridflux-website/src/components/Hero.tsx
+++ b/offgridflux-website/src/components/Hero.tsx
@@ -28,7 +28,7 @@ export function Hero() {
             {/* Main Headline */}
             <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold leading-tight mb-6">
               Unplugged Power,{' '}
-              <span className="text-transparent bg-clip-text bg-gradient-to-r from-yellow-300 to-orange-300">
+              <span className="text-transparent bg-clip-text bg-gradient-to-r from-warning-300 to-warning-400">
                 Real-World Testing
               </span>
             </h1>
@@ -42,13 +42,13 @@ export function Hero() {
             {/* Trust Indicators */}
             <div className="flex flex-wrap justify-center lg:justify-start gap-6 mb-8 text-sm">
               <div className="flex items-center">
-                <Shield className="w-5 h-5 mr-2 text-green-300" />
+                <Shield className="w-5 h-5 mr-2 text-energy-300" />
                 <span>100% Independent</span>
               </div>
               <div className="flex items-center">
-                <Zap className="w-5 h-5 mr-2 text-yellow-300" />
+                <Zap className="w-5 h-5 mr-2 text-warning-300" />
                 <span>Real-World Testing</span>
               </div>
               <div className="flex items-center">
-                <Users className="w-5 h-5 mr-2 text-blue-300" />
+                <Users className="w-5 h-5 mr-2 text-primary-300" />
                 <span>10,000+ Readers</span>
               </div>
             </div>
@@ -90,13 +90,13 @@ export function Hero() {
               </div>
               
               <div className="absolute -bottom-4 -right-4 bg-white text-gray-900 rounded-lg p-3 shadow-lg">
-                <div className="text-2xl font-bold text-green-600">4.8★</div>
+                <div className="text-2xl font-bold text-energy-600">4.8★</div>
                 <div className="text-sm text-gray-600">Avg Rating</div>
               </div>
               
               <div className="absolute top-1/2 -right-6 bg-white text-gray-900 rounded-lg p-3 shadow-lg transform -translate-y-1/2">
-                <div className="text-2xl font-bold text-orange-600">30+</div>
+                <div className="text-2xl font-bold text-warning-600">30+</div>
                 <div className="text-sm text-gray-600">Days Testing</div>
               </div>
             </div>

diff --git a/offgridflux-website/src/components/CategoryGrid.tsx b/offgridflux-website/src/components/CategoryGrid.tsx
index 3456789..cdefghi 100644
--- a/offgridflux-website/src/components/CategoryGrid.tsx
+++ b/offgridflux-website/src/components/CategoryGrid.tsx
@@ -25,7 +25,7 @@ const categories: Category[] = [
     description: 'Compact power for camping, emergencies, and outdoor adventures',
     icon: Zap,
     gradient: 'from-blue-50 to-blue-100',
-    iconBg: 'bg-blue-600',
+    iconBg: 'bg-primary-600',
     productCount: 12,
     priceRange: '$149 - $999',
     featured: true,
@@ -36,7 +36,7 @@ const categories: Category[] = [
     description: 'Renewable power solutions with integrated solar panels',
     icon: Sun,
     gradient: 'from-yellow-50 to-yellow-100',
-    iconBg: 'bg-yellow-600',
+    iconBg: 'bg-warning-600',
     productCount: 8,
     priceRange: '$299 - $1,499',
   },
@@ -46,7 +46,7 @@ const categories: Category[] = [
     description: 'Whole home backup power and grid independence solutions',
     icon: Home,
     gradient: 'from-green-50 to-green-100',
-    iconBg: 'bg-green-600',
+    iconBg: 'bg-energy-600',
     productCount: 5,
     priceRange: '$2,999 - $5,799',
   },
