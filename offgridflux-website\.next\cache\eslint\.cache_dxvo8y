[{"C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\app\\about\\page.tsx": "1", "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\app\\best-portable-power-stations-2025\\page.tsx": "2", "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\app\\blog\\[year]\\[month]\\[slug]\\page.tsx": "3", "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\app\\category\\[slug]\\page.tsx": "4", "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\app\\guides\\portable-power-station-buying-guide\\page.tsx": "5", "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\app\\layout.tsx": "6", "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\app\\page.tsx": "7", "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\app\\product\\[slug]\\page.tsx": "8", "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\app\\reviews\\jackery-explorer-1000-v2-review\\page.tsx": "9", "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\AffiliateDisclosure.tsx": "10", "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\BlogContent.tsx": "11", "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\BlogHeader.tsx": "12", "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\BlogSidebar.tsx": "13", "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\CategoryGrid.tsx": "14", "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\CategoryHero.tsx": "15", "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\ComparisonTable.tsx": "16", "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\Footer.tsx": "17", "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\GoogleAnalytics.tsx": "18", "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\Header.tsx": "19", "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\Hero.tsx": "20", "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\NewsletterSignup.tsx": "21", "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\ProductCard.tsx": "22", "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\ProductHero.tsx": "23", "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\ProsAndCons.tsx": "24", "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\RelatedPosts.tsx": "25", "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\RelatedProducts.tsx": "26", "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\RuntimeCalculator.tsx": "27", "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\SpecificationTable.tsx": "28", "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\StructuredData.tsx": "29", "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\TeamMember.tsx": "30", "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\TestingProcess.tsx": "31", "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\TrustSignals.tsx": "32", "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\ui\\badge.tsx": "33", "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\ui\\button.tsx": "34", "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\ui\\card.tsx": "35", "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\ui\\input.tsx": "36", "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\ui\\textarea.tsx": "37", "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\lib\\analytics.ts": "38", "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\lib\\blog.ts": "39", "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\lib\\supabase.ts": "40", "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\lib\\utils.ts": "41"}, {"size": 9883, "mtime": 1748665952004, "results": "42", "hashOfConfig": "43"}, {"size": 13643, "mtime": 1748703082425, "results": "44", "hashOfConfig": "43"}, {"size": 3464, "mtime": 1748668079653, "results": "45", "hashOfConfig": "43"}, {"size": 6501, "mtime": 1748702410146, "results": "46", "hashOfConfig": "43"}, {"size": 10022, "mtime": 1748703417101, "results": "47", "hashOfConfig": "43"}, {"size": 2375, "mtime": 1748717425332, "results": "48", "hashOfConfig": "43"}, {"size": 4693, "mtime": 1748717562241, "results": "49", "hashOfConfig": "43"}, {"size": 9760, "mtime": 1748702486320, "results": "50", "hashOfConfig": "43"}, {"size": 14528, "mtime": 1748703656700, "results": "51", "hashOfConfig": "43"}, {"size": 3948, "mtime": 1748705919109, "results": "52", "hashOfConfig": "43"}, {"size": 1814, "mtime": 1748667667600, "results": "53", "hashOfConfig": "43"}, {"size": 2310, "mtime": 1748667681027, "results": "54", "hashOfConfig": "43"}, {"size": 3600, "mtime": 1748666948846, "results": "55", "hashOfConfig": "43"}, {"size": 6263, "mtime": 1748717512632, "results": "56", "hashOfConfig": "43"}, {"size": 2694, "mtime": 1748666997650, "results": "57", "hashOfConfig": "43"}, {"size": 6028, "mtime": 1748667015864, "results": "58", "hashOfConfig": "43"}, {"size": 6077, "mtime": 1748717416936, "results": "59", "hashOfConfig": "43"}, {"size": 3416, "mtime": 1748703710839, "results": "60", "hashOfConfig": "43"}, {"size": 6952, "mtime": 1748717394859, "results": "61", "hashOfConfig": "43"}, {"size": 7542, "mtime": 1748716734801, "results": "62", "hashOfConfig": "43"}, {"size": 7391, "mtime": 1748718205156, "results": "63", "hashOfConfig": "43"}, {"size": 8459, "mtime": 1748717499552, "results": "64", "hashOfConfig": "43"}, {"size": 6332, "mtime": 1748667037021, "results": "65", "hashOfConfig": "43"}, {"size": 1382, "mtime": 1748667058907, "results": "66", "hashOfConfig": "43"}, {"size": 1727, "mtime": 1748666959616, "results": "67", "hashOfConfig": "43"}, {"size": 524, "mtime": 1748667080126, "results": "68", "hashOfConfig": "43"}, {"size": 2879, "mtime": 1748667693076, "results": "69", "hashOfConfig": "43"}, {"size": 1322, "mtime": 1748667049511, "results": "70", "hashOfConfig": "43"}, {"size": 1138, "mtime": 1748702638575, "results": "71", "hashOfConfig": "43"}, {"size": 944, "mtime": 1748667753421, "results": "72", "hashOfConfig": "43"}, {"size": 2411, "mtime": 1748666901362, "results": "73", "hashOfConfig": "43"}, {"size": 4168, "mtime": 1748667472424, "results": "74", "hashOfConfig": "43"}, {"size": 1631, "mtime": 1748666689877, "results": "75", "hashOfConfig": "43"}, {"size": 2123, "mtime": 1748666689805, "results": "76", "hashOfConfig": "43"}, {"size": 1989, "mtime": 1748666689861, "results": "77", "hashOfConfig": "43"}, {"size": 967, "mtime": 1748666689886, "results": "78", "hashOfConfig": "43"}, {"size": 759, "mtime": 1748666689895, "results": "79", "hashOfConfig": "43"}, {"size": 8401, "mtime": 1748703465787, "results": "80", "hashOfConfig": "43"}, {"size": 1939, "mtime": 1748667766941, "results": "81", "hashOfConfig": "43"}, {"size": 5657, "mtime": 1748702677756, "results": "82", "hashOfConfig": "43"}, {"size": 166, "mtime": 1748666599021, "results": "83", "hashOfConfig": "43"}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "12r5y0f", {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "162", "messages": "163", "suppressedMessages": "164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "165", "messages": "166", "suppressedMessages": "167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "168", "messages": "169", "suppressedMessages": "170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "171", "messages": "172", "suppressedMessages": "173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "177", "messages": "178", "suppressedMessages": "179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "180", "messages": "181", "suppressedMessages": "182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "183", "messages": "184", "suppressedMessages": "185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "186", "messages": "187", "suppressedMessages": "188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "189", "messages": "190", "suppressedMessages": "191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "192", "messages": "193", "suppressedMessages": "194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "195", "messages": "196", "suppressedMessages": "197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "198", "messages": "199", "suppressedMessages": "200", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "201", "messages": "202", "suppressedMessages": "203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "204", "messages": "205", "suppressedMessages": "206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\app\\about\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\app\\best-portable-power-stations-2025\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\app\\blog\\[year]\\[month]\\[slug]\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\app\\category\\[slug]\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\app\\guides\\portable-power-station-buying-guide\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\app\\product\\[slug]\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\app\\reviews\\jackery-explorer-1000-v2-review\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\AffiliateDisclosure.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\BlogContent.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\BlogHeader.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\BlogSidebar.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\CategoryGrid.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\CategoryHero.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\ComparisonTable.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\Footer.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\GoogleAnalytics.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\Header.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\Hero.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\NewsletterSignup.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\ProductCard.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\ProductHero.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\ProsAndCons.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\RelatedPosts.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\RelatedProducts.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\RuntimeCalculator.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\SpecificationTable.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\StructuredData.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\TeamMember.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\TestingProcess.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\TrustSignals.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\ui\\badge.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\ui\\button.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\ui\\card.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\ui\\input.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\ui\\textarea.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\lib\\analytics.ts", [], [], "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\lib\\blog.ts", [], [], "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\lib\\supabase.ts", [], [], "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\lib\\utils.ts", [], []]