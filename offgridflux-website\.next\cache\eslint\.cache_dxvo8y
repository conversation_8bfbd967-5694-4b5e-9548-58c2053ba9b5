[{"C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\app\\about\\page.tsx": "1", "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\app\\blog\\[year]\\[month]\\[slug]\\page.tsx": "2", "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\app\\category\\[slug]\\page.tsx": "3", "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\app\\layout.tsx": "4", "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\app\\page.tsx": "5", "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\app\\product\\[slug]\\page.tsx": "6", "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\AffiliateDisclosure.tsx": "7", "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\BlogContent.tsx": "8", "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\BlogHeader.tsx": "9", "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\BlogSidebar.tsx": "10", "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\CategoryGrid.tsx": "11", "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\CategoryHero.tsx": "12", "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\ComparisonTable.tsx": "13", "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\Hero.tsx": "14", "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\NewsletterSignup.tsx": "15", "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\ProductCard.tsx": "16", "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\ProductHero.tsx": "17", "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\ProsAndCons.tsx": "18", "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\RelatedPosts.tsx": "19", "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\RelatedProducts.tsx": "20", "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\RuntimeCalculator.tsx": "21", "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\SpecificationTable.tsx": "22", "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\StructuredData.tsx": "23", "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\TeamMember.tsx": "24", "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\TestingProcess.tsx": "25", "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\TrustSignals.tsx": "26", "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\ui\\badge.tsx": "27", "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\ui\\button.tsx": "28", "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\ui\\card.tsx": "29", "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\ui\\input.tsx": "30", "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\ui\\textarea.tsx": "31", "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\lib\\blog.ts": "32", "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\lib\\supabase.ts": "33", "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\lib\\utils.ts": "34", "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\app\\best-portable-power-stations-2025\\page.tsx": "35", "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\app\\guides\\portable-power-station-buying-guide\\page.tsx": "36", "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\app\\reviews\\jackery-explorer-1000-v2-review\\page.tsx": "37", "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\GoogleAnalytics.tsx": "38", "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\lib\\analytics.ts": "39"}, {"size": 9883, "mtime": 1748665952004, "results": "40", "hashOfConfig": "41"}, {"size": 3464, "mtime": 1748668079653, "results": "42", "hashOfConfig": "41"}, {"size": 6501, "mtime": 1748702410146, "results": "43", "hashOfConfig": "41"}, {"size": 2083, "mtime": 1748703251726, "results": "44", "hashOfConfig": "41"}, {"size": 3906, "mtime": 1748667431408, "results": "45", "hashOfConfig": "41"}, {"size": 9760, "mtime": 1748702486320, "results": "46", "hashOfConfig": "41"}, {"size": 3942, "mtime": 1748667594523, "results": "47", "hashOfConfig": "41"}, {"size": 1814, "mtime": 1748667667600, "results": "48", "hashOfConfig": "41"}, {"size": 2310, "mtime": 1748667681027, "results": "49", "hashOfConfig": "41"}, {"size": 3600, "mtime": 1748666948846, "results": "50", "hashOfConfig": "41"}, {"size": 5861, "mtime": 1748667458512, "results": "51", "hashOfConfig": "41"}, {"size": 2694, "mtime": 1748666997650, "results": "52", "hashOfConfig": "41"}, {"size": 6028, "mtime": 1748667015864, "results": "53", "hashOfConfig": "41"}, {"size": 6536, "mtime": 1748667116460, "results": "54", "hashOfConfig": "41"}, {"size": 6448, "mtime": 1748666794355, "results": "55", "hashOfConfig": "41"}, {"size": 7257, "mtime": 1748703315179, "results": "56", "hashOfConfig": "41"}, {"size": 6332, "mtime": 1748667037021, "results": "57", "hashOfConfig": "41"}, {"size": 1382, "mtime": 1748667058907, "results": "58", "hashOfConfig": "41"}, {"size": 1727, "mtime": 1748666959616, "results": "59", "hashOfConfig": "41"}, {"size": 524, "mtime": 1748667080126, "results": "60", "hashOfConfig": "41"}, {"size": 2879, "mtime": 1748667693076, "results": "61", "hashOfConfig": "41"}, {"size": 1322, "mtime": 1748667049511, "results": "62", "hashOfConfig": "41"}, {"size": 1138, "mtime": 1748702638575, "results": "63", "hashOfConfig": "41"}, {"size": 944, "mtime": 1748667753421, "results": "64", "hashOfConfig": "41"}, {"size": 2411, "mtime": 1748666901362, "results": "65", "hashOfConfig": "41"}, {"size": 4168, "mtime": 1748667472424, "results": "66", "hashOfConfig": "41"}, {"size": 1631, "mtime": 1748666689877, "results": "67", "hashOfConfig": "41"}, {"size": 2123, "mtime": 1748666689805, "results": "68", "hashOfConfig": "41"}, {"size": 1989, "mtime": 1748666689861, "results": "69", "hashOfConfig": "41"}, {"size": 967, "mtime": 1748666689886, "results": "70", "hashOfConfig": "41"}, {"size": 759, "mtime": 1748666689895, "results": "71", "hashOfConfig": "41"}, {"size": 1939, "mtime": 1748667766941, "results": "72", "hashOfConfig": "41"}, {"size": 5657, "mtime": 1748702677756, "results": "73", "hashOfConfig": "41"}, {"size": 166, "mtime": 1748666599021, "results": "74", "hashOfConfig": "41"}, {"size": 13643, "mtime": 1748703082425, "results": "75", "hashOfConfig": "41"}, {"size": 10022, "mtime": 1748703417101, "results": "76", "hashOfConfig": "41"}, {"size": 14528, "mtime": 1748703656700, "results": "77", "hashOfConfig": "41"}, {"size": 3416, "mtime": 1748703710839, "results": "78", "hashOfConfig": "41"}, {"size": 8401, "mtime": 1748703465787, "results": "79", "hashOfConfig": "41"}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "12r5y0f", {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "182", "messages": "183", "suppressedMessages": "184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "185", "messages": "186", "suppressedMessages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "191", "messages": "192", "suppressedMessages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\app\\about\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\app\\blog\\[year]\\[month]\\[slug]\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\app\\category\\[slug]\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\app\\product\\[slug]\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\AffiliateDisclosure.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\BlogContent.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\BlogHeader.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\BlogSidebar.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\CategoryGrid.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\CategoryHero.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\ComparisonTable.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\Hero.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\NewsletterSignup.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\ProductCard.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\ProductHero.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\ProsAndCons.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\RelatedPosts.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\RelatedProducts.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\RuntimeCalculator.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\SpecificationTable.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\StructuredData.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\TeamMember.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\TestingProcess.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\TrustSignals.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\ui\\badge.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\ui\\button.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\ui\\card.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\ui\\input.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\ui\\textarea.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\lib\\blog.ts", [], [], "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\lib\\supabase.ts", [], [], "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\lib\\utils.ts", [], [], "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\app\\best-portable-power-stations-2025\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\app\\guides\\portable-power-station-buying-guide\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\app\\reviews\\jackery-explorer-1000-v2-review\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\GoogleAnalytics.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\lib\\analytics.ts", [], []]