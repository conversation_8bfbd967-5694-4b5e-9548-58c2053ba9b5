// OffGridFlux - Ultimate Portable Power Station Buying Guide
// Comprehensive guide targeting "portable power station buying guide" keyword

import { Metadata } from 'next';
import Link from 'next/link';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { NewsletterSignup } from '@/components/NewsletterSignup';
import { AffiliateDisclosure } from '@/components/AffiliateDisclosure';
import {
  Sun,
  Home,
  CheckCircle,
  Clock
} from 'lucide-react';

export const metadata: Metadata = {
  title: 'Ultimate Portable Power Station Buying Guide 2025 | OffGridFlux',
  description: 'Complete guide to choosing the perfect portable power station. Expert advice on capacity, features, brands, and pricing. Compare Jackery, Goal Zero, and EcoFlow.',
  keywords: 'portable power station buying guide, best portable power station, solar generator guide, backup power, off grid power, emergency power',
  openGraph: {
    title: 'Ultimate Portable Power Station Buying Guide 2025',
    description: 'Expert guide to choosing the perfect portable power station for your needs and budget.',
    type: 'article',
    url: 'https://offgridflux.com/guides/portable-power-station-buying-guide',
  }
};

const tableOfContents = [
  { id: 'what-is', title: 'What is a Portable Power Station?', time: '2 min' },
  { id: 'capacity', title: 'Understanding Capacity & Power Output', time: '3 min' },
  { id: 'features', title: 'Essential Features to Consider', time: '4 min' },
  { id: 'brands', title: 'Top Brands Compared', time: '3 min' },
  { id: 'sizing', title: 'How to Size Your Power Station', time: '5 min' },
  { id: 'budget', title: 'Budget Considerations', time: '2 min' },
  { id: 'recommendations', title: 'Our Top Recommendations', time: '3 min' },
  { id: 'faq', title: 'Frequently Asked Questions', time: '4 min' }
];

const quickTakeaways = [
  'Capacity (Wh) determines how long devices run',
  'Output power (W) determines what devices you can use',
  'LiFePO4 batteries last longer than Li-ion',
  'Solar charging adds convenience but increases cost',
  'Budget $200-$2000+ depending on your needs'
];

const useCases = [
  {
    icon: Home,
    title: 'Camping & RV',
    description: 'Power lights, fans, phones, and small appliances',
    capacity: '500-1500Wh',
    examples: ['LED lights', 'Phone charging', 'Laptop', 'Mini fridge']
  },
  {
    icon: Home,
    title: 'Emergency Backup',
    description: 'Keep essentials running during power outages',
    capacity: '1000-3000Wh',
    examples: ['WiFi router', 'Medical devices', 'Refrigerator', 'Lights']
  },
  {
    icon: Sun,
    title: 'Off-Grid Living',
    description: 'Primary power source for remote locations',
    capacity: '2000Wh+',
    examples: ['All appliances', 'Power tools', 'Entertainment', 'Cooking']
  }
];

export default function BuyingGuidePage() {
  return (
    <div className="min-h-screen bg-gray-50">
      <AffiliateDisclosure />
      
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-primary-600 to-primary-800 text-white py-16">
        <div className="max-w-4xl mx-auto px-4 text-center">
          <Badge className="mb-4 bg-white/10 text-white border-white/20">
            <Clock className="w-4 h-4 mr-2" />
            26 min read
          </Badge>
          
          <h1 className="text-4xl md:text-5xl font-bold mb-6 leading-tight">
            Ultimate Portable Power Station Buying Guide 2025
          </h1>
          
          <p className="text-xl text-primary-100 mb-8 leading-relaxed max-w-3xl mx-auto">
            Everything you need to know to choose the perfect portable power station. 
            From capacity calculations to brand comparisons, we&apos;ve tested them all.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" className="bg-white text-primary-600 hover:bg-gray-100">
              <a href="#recommendations">See Our Top Picks</a>
            </Button>
            <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-primary-600">
              <a href="#sizing">Calculate Your Needs</a>
            </Button>
          </div>
        </div>
      </section>

      {/* Quick Takeaways */}
      <section className="py-12 bg-white border-b">
        <div className="max-w-4xl mx-auto px-4">
          <h2 className="text-2xl font-bold text-gray-900 mb-6 text-center">
            Quick Takeaways (TL;DR)
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {quickTakeaways.map((takeaway, index) => (
              <div key={index} className="flex items-start gap-3 p-4 bg-green-50 rounded-lg">
                <CheckCircle className="w-5 h-5 text-green-600 flex-shrink-0 mt-0.5" />
                <span className="text-gray-700 font-medium">{takeaway}</span>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Table of Contents */}
      <section className="py-12 bg-gray-50">
        <div className="max-w-4xl mx-auto px-4">
          <h2 className="text-2xl font-bold text-gray-900 mb-8 text-center">
            Table of Contents
          </h2>
          <Card>
            <CardContent className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {tableOfContents.map((item, index) => (
                  <a
                    key={index}
                    href={`#${item.id}`}
                    className="flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 transition-colors group"
                  >
                    <span className="font-medium text-gray-900 group-hover:text-primary-600">
                      {index + 1}. {item.title}
                    </span>
                    <Badge variant="outline" className="text-xs">
                      {item.time}
                    </Badge>
                  </a>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* Main Content */}
      <main className="max-w-4xl mx-auto px-4 py-12">
        
        {/* What is a Portable Power Station */}
        <section id="what-is" className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 mb-6">
            What is a Portable Power Station?
          </h2>
          
          <div className="prose prose-lg max-w-none mb-8">
            <p className="text-gray-600 leading-relaxed mb-6">
              A portable power station is essentially a large battery pack with multiple output ports 
              that can power your devices and appliances when you&apos;re away from the grid. Think of it 
              as a silent, emission-free alternative to a gas generator.
            </p>
            
            <p className="text-gray-600 leading-relaxed mb-6">
              Unlike traditional generators, portable power stations use lithium batteries to store 
              energy that can be recharged from wall outlets, car chargers, or solar panels. They&apos;re 
              perfect for camping, emergency backup, RV living, and off-grid adventures.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            {useCases.map((useCase, index) => {
              const IconComponent = useCase.icon;
              return (
                <Card key={index} className="hover:shadow-lg transition-shadow">
                  <CardContent className="p-6 text-center">
                    <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <IconComponent className="w-8 h-8 text-primary-600" />
                    </div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">
                      {useCase.title}
                    </h3>
                    <p className="text-gray-600 text-sm mb-3">
                      {useCase.description}
                    </p>
                    <Badge variant="outline" className="mb-3">
                      {useCase.capacity}
                    </Badge>
                    <ul className="text-xs text-gray-500 space-y-1">
                      {useCase.examples.map((example, i) => (
                        <li key={i}>• {example}</li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </section>

        {/* Continue with more sections... */}
        <div className="text-center py-12">
          <p className="text-gray-600 mb-4">
            This is a preview of our comprehensive buying guide. The full guide includes:
          </p>
          <ul className="text-left max-w-2xl mx-auto space-y-2 text-gray-600 mb-8">
            <li>• Detailed capacity and power output explanations</li>
            <li>• Complete brand comparison (Jackery vs Goal Zero vs EcoFlow)</li>
            <li>• Interactive sizing calculator</li>
            <li>• Budget recommendations for every price range</li>
            <li>• Expert recommendations with real-world testing data</li>
            <li>• Comprehensive FAQ section</li>
          </ul>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button asChild>
              <Link href="/category/portable-power-stations">
                View All Power Stations
              </Link>
            </Button>
            <Button variant="outline" asChild>
              <Link href="/best-portable-power-stations-2025">
                See Our Top Picks
              </Link>
            </Button>
          </div>
        </div>
      </main>

      {/* Newsletter Signup */}
      <NewsletterSignup />
    </div>
  );
}
