// OffGridFlux - Product Card Component
// Displays product information in a card format for category and homepage listings

'use client';

import Link from 'next/link';
import Image from 'next/image';
import { useEffect } from 'react';
import { Product } from '@/lib/supabase';
import { useAffiliateTracking, useProductTracking } from '@/components/GoogleAnalytics';
import { Battery, Zap, Weight, Star } from 'lucide-react';

interface ProductCardProps {
  product: Product;
}

export function ProductCard({ product }: ProductCardProps) {
  const { trackClick } = useAffiliateTracking();
  const { trackView } = useProductTracking();

  const valueScore = product.current_price_usd ?
    (product.capacity_wh / parseFloat(String(product.current_price_usd))).toFixed(2) : null;

  // Track product view on mount
  useEffect(() => {
    trackView(
      product.product_name,
      product.slug,
      product.brand,
      product.current_price_usd || 0,
      'Portable Power Station'
    );
  }, [product, trackView]);

  const handleAffiliateClick = () => {
    trackClick(
      product.product_name,
      product.slug,
      product.brand,
      product.current_price_usd || 0,
      product.affiliate_url || ''
    );
  };

  return (
    <div className="bg-white rounded-lg shadow-soft hover:shadow-medium transition-shadow duration-300 overflow-hidden">
      {/* Product Image */}
      <div className="relative h-48 bg-gray-100">
        {product.product_image_url ? (
          <Image
            src={product.product_image_url}
            alt={product.product_name}
            fill
            className="object-contain p-4"
          />
        ) : (
          <div className="flex items-center justify-center h-full">
            <Battery className="w-16 h-16 text-gray-400" />
          </div>
        )}
        
        {/* Discount Badge */}
        {product.discount_percent && (
          <div className="absolute top-2 right-2 bg-warning-500 text-white text-sm font-medium px-2 py-1 rounded">
            {product.discount_percent}% OFF
          </div>
        )}
        
        {/* Brand Badge */}
        <div className="absolute top-2 left-2 bg-white/90 backdrop-blur-sm text-gray-700 text-sm font-medium px-2 py-1 rounded">
          {product.brand}
        </div>
      </div>

      {/* Product Info */}
      <div className="p-4">
        {/* Product Name */}
        <h3 className="font-semibold text-gray-900 mb-2 line-clamp-2">
          <Link 
            href={`/product/${product.slug}`}
            className="hover:text-primary-600 transition-colors"
          >
            {product.product_name}
          </Link>
        </h3>

        {/* Key Specs */}
        <div className="grid grid-cols-2 gap-2 mb-3 text-sm">
          <div className="flex items-center text-gray-600">
            <Battery className="w-4 h-4 mr-1" />
            {product.capacity_wh}Wh
          </div>
          <div className="flex items-center text-gray-600">
            <Zap className="w-4 h-4 mr-1" />
            {product.max_output_w}W
          </div>
          {product.weight_lbs && (
            <div className="flex items-center text-gray-600">
              <Weight className="w-4 h-4 mr-1" />
              {product.weight_lbs}lbs
            </div>
          )}
          {product.battery_type && (
            <div className="text-gray-600 text-xs">
              {product.battery_type}
            </div>
          )}
        </div>

        {/* Rating */}
        {product.offgridflux_rating && (
          <div className="flex items-center mb-3">
            <div className="flex items-center">
              {[...Array(5)].map((_, i) => (
                <Star
                  key={i}
                  className={`w-4 h-4 ${
                    i < Math.floor(product.offgridflux_rating!)
                      ? 'text-warning-400 fill-current'
                      : 'text-neutral-300'
                  }`}
                />
              ))}
            </div>
            <span className="ml-2 text-sm text-gray-600">
              {product.offgridflux_rating}/10
            </span>
          </div>
        )}

        {/* Key Features */}
        {product.key_features && product.key_features.length > 0 && (
          <div className="mb-3">
            <ul className="text-sm text-gray-600 space-y-1">
              {product.key_features.slice(0, 2).map((feature, index) => (
                <li key={index} className="flex items-center">
                  <span className="w-1.5 h-1.5 bg-green-500 rounded-full mr-2 flex-shrink-0"></span>
                  {feature}
                </li>
              ))}
            </ul>
          </div>
        )}

        {/* Price and CTA */}
        <div className="border-t pt-3">
          <div className="flex items-center justify-between mb-2">
            <div>
              <div className="text-2xl font-bold text-gray-900">
                ${product.current_price_usd}
              </div>
              {product.msrp_usd && product.msrp_usd !== product.current_price_usd && (
                <div className="text-sm text-gray-500 line-through">
                  ${product.msrp_usd}
                </div>
              )}
            </div>
            {valueScore && (
              <div className="text-right">
                <div className="text-xs text-gray-500">Value Score</div>
                <div className="text-sm font-medium text-energy-600">
                  {valueScore} Wh/$
                </div>
              </div>
            )}
          </div>

          <div className="space-y-2">
            <Link
              href={`/product/${product.slug}`}
              className="block w-full bg-primary-600 hover:bg-primary-700 text-white text-center py-2 px-4 rounded-lg transition-colors duration-200 font-medium"
            >
              View Review
            </Link>
            
            {product.affiliate_url && (
              <a
                href={product.affiliate_url}
                target="_blank"
                rel="noopener noreferrer"
                onClick={handleAffiliateClick}
                aria-label={`Check price for ${product.product_name} on retailer site`}
                className="block w-full bg-neutral-100 hover:bg-neutral-200 text-neutral-700 text-center py-2 px-4 rounded-lg transition-colors duration-200 text-sm"
              >
                Check Price
              </a>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

// Skeleton loader for loading states
export function ProductCardSkeleton() {
  return (
    <div className="bg-white rounded-lg shadow-soft overflow-hidden animate-pulse">
      <div className="h-48 bg-gray-200"></div>
      <div className="p-4">
        <div className="h-4 bg-gray-200 rounded mb-2"></div>
        <div className="h-4 bg-gray-200 rounded w-3/4 mb-3"></div>
        <div className="grid grid-cols-2 gap-2 mb-3">
          <div className="h-3 bg-gray-200 rounded"></div>
          <div className="h-3 bg-gray-200 rounded"></div>
        </div>
        <div className="h-3 bg-gray-200 rounded mb-3"></div>
        <div className="border-t pt-3">
          <div className="h-6 bg-gray-200 rounded mb-2"></div>
          <div className="h-8 bg-gray-200 rounded"></div>
        </div>
      </div>
    </div>
  );
}
