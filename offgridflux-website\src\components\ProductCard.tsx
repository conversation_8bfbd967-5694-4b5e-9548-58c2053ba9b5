// OffGridFlux - Product Card Component
// Displays product information in a card format for category and homepage listings

'use client';

import Link from 'next/link';
import Image from 'next/image';
import { useEffect } from 'react';
import { Product } from '@/lib/supabase';
import { useAffiliateTracking, useProductTracking } from '@/components/GoogleAnalytics';
import { Battery, Zap, Weight, Star } from 'lucide-react';

interface ProductCardProps {
  product: Product;
}

export function ProductCard({ product }: ProductCardProps) {
  const { trackClick } = useAffiliateTracking();
  const { trackView } = useProductTracking();

  const valueScore = product.current_price_usd ?
    (product.capacity_wh / parseFloat(String(product.current_price_usd))).toFixed(2) : null;

  // Track product view on mount
  useEffect(() => {
    trackView(
      product.product_name,
      product.slug,
      product.brand,
      product.current_price_usd || 0,
      'Portable Power Station'
    );
  }, [product, trackView]);

  const handleAffiliateClick = () => {
    trackClick(
      product.product_name,
      product.slug,
      product.brand,
      product.current_price_usd || 0,
      product.affiliate_url || ''
    );
  };

  return (
    <div className="group bg-white rounded-xl shadow-soft hover:shadow-large transition-all duration-300 overflow-hidden border border-neutral-100 hover:border-primary-200 hover:-translate-y-1">
      {/* Product Image */}
      <div className="relative h-52 bg-gradient-to-br from-neutral-50 to-neutral-100">
        {product.product_image_url ? (
          <Image
            src={product.product_image_url}
            alt={product.product_name}
            fill
            className="object-contain p-6 group-hover:scale-105 transition-transform duration-300"
          />
        ) : (
          <div className="flex items-center justify-center h-full">
            <Battery className="w-16 h-16 text-neutral-400" />
          </div>
        )}

        {/* Discount Badge */}
        {product.discount_percent && (
          <div className="absolute top-3 right-3 bg-gradient-to-r from-warning-500 to-warning-600 text-white text-sm font-bold px-3 py-1.5 rounded-full shadow-lg">
            {product.discount_percent}% OFF
          </div>
        )}

        {/* Brand Badge */}
        <div className="absolute top-3 left-3 bg-white/95 backdrop-blur-sm text-neutral-700 text-sm font-semibold px-3 py-1.5 rounded-full border border-neutral-200 shadow-sm">
          {product.brand}
        </div>

        {/* Quick View Overlay */}
        <div className="absolute inset-0 bg-black/0 group-hover:bg-black/5 transition-colors duration-300"></div>
      </div>

      {/* Product Info */}
      <div className="p-6">
        {/* Product Name */}
        <h3 className="font-bold text-neutral-900 mb-3 text-lg leading-tight">
          <Link
            href={`/product/${product.slug}`}
            className="hover:text-primary-600 transition-colors group-hover:text-primary-600"
          >
            {product.product_name}
          </Link>
        </h3>

        {/* Key Specs */}
        <div className="grid grid-cols-2 gap-3 mb-4">
          <div className="flex items-center gap-2 text-neutral-600 bg-neutral-50 rounded-lg px-3 py-2">
            <Battery className="w-4 h-4 text-primary-500" />
            <span className="font-medium text-sm">{product.capacity_wh}Wh</span>
          </div>
          <div className="flex items-center gap-2 text-neutral-600 bg-neutral-50 rounded-lg px-3 py-2">
            <Zap className="w-4 h-4 text-warning-500" />
            <span className="font-medium text-sm">{product.max_output_w}W</span>
          </div>
          {product.weight_lbs && (
            <div className="flex items-center gap-2 text-neutral-600 bg-neutral-50 rounded-lg px-3 py-2">
              <Weight className="w-4 h-4 text-energy-500" />
              <span className="font-medium text-sm">{product.weight_lbs}lbs</span>
            </div>
          )}
          {product.battery_type && (
            <div className="bg-neutral-50 rounded-lg px-3 py-2">
              <span className="text-neutral-600 font-medium text-xs">{product.battery_type}</span>
            </div>
          )}
        </div>

        {/* Rating */}
        {product.offgridflux_rating && (
          <div className="flex items-center mb-3">
            <div className="flex items-center">
              {[...Array(5)].map((_, i) => (
                <Star
                  key={i}
                  className={`w-4 h-4 ${
                    i < Math.floor(product.offgridflux_rating!)
                      ? 'text-warning-400 fill-current'
                      : 'text-neutral-300'
                  }`}
                />
              ))}
            </div>
            <span className="ml-2 text-sm text-gray-600">
              {product.offgridflux_rating}/10
            </span>
          </div>
        )}

        {/* Key Features */}
        {product.key_features && product.key_features.length > 0 && (
          <div className="mb-3">
            <ul className="text-sm text-gray-600 space-y-1">
              {product.key_features.slice(0, 2).map((feature, index) => (
                <li key={index} className="flex items-center">
                  <span className="w-1.5 h-1.5 bg-green-500 rounded-full mr-2 flex-shrink-0"></span>
                  {feature}
                </li>
              ))}
            </ul>
          </div>
        )}

        {/* Price and CTA */}
        <div className="border-t border-neutral-100 pt-4 mt-4">
          <div className="flex items-center justify-between mb-4">
            <div>
              <div className="text-2xl font-bold text-neutral-900">
                ${product.current_price_usd}
              </div>
              {product.msrp_usd && product.msrp_usd !== product.current_price_usd && (
                <div className="text-sm text-neutral-500 line-through">
                  ${product.msrp_usd}
                </div>
              )}
            </div>
            {valueScore && (
              <div className="text-right bg-energy-50 rounded-lg px-3 py-2">
                <div className="text-xs text-energy-600 font-medium">Value Score</div>
                <div className="text-lg font-bold text-energy-700">
                  {valueScore}
                </div>
                <div className="text-xs text-energy-600">Wh/$</div>
              </div>
            )}
          </div>

          <div className="space-y-3">
            <Link
              href={`/product/${product.slug}`}
              className="block w-full bg-gradient-to-r from-primary-600 to-primary-700 hover:from-primary-700 hover:to-primary-800 text-white text-center py-3 px-4 rounded-xl transition-all duration-200 font-semibold shadow-lg hover:shadow-xl hover:scale-105"
            >
              View Full Review
            </Link>

            {product.affiliate_url && (
              <a
                href={product.affiliate_url}
                target="_blank"
                rel="noopener noreferrer"
                onClick={handleAffiliateClick}
                aria-label={`Check price for ${product.product_name} on retailer site`}
                className="block w-full bg-neutral-100 hover:bg-neutral-200 text-neutral-700 text-center py-3 px-4 rounded-xl transition-all duration-200 font-medium border border-neutral-200 hover:border-neutral-300"
              >
                Check Current Price
              </a>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

// Skeleton loader for loading states
export function ProductCardSkeleton() {
  return (
    <div className="bg-white rounded-lg shadow-soft overflow-hidden animate-pulse">
      <div className="h-48 bg-gray-200"></div>
      <div className="p-4">
        <div className="h-4 bg-gray-200 rounded mb-2"></div>
        <div className="h-4 bg-gray-200 rounded w-3/4 mb-3"></div>
        <div className="grid grid-cols-2 gap-2 mb-3">
          <div className="h-3 bg-gray-200 rounded"></div>
          <div className="h-3 bg-gray-200 rounded"></div>
        </div>
        <div className="h-3 bg-gray-200 rounded mb-3"></div>
        <div className="border-t pt-3">
          <div className="h-6 bg-gray-200 rounded mb-2"></div>
          <div className="h-8 bg-gray-200 rounded"></div>
        </div>
      </div>
    </div>
  );
}
