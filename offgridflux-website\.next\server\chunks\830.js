"use strict";exports.id=830,exports.ids=[830],exports.modules={349:(e,s,a)=>{a.d(s,{R:()=>i});var t=a(7413),l=a(1142);let r=(0,a(6373).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]);function i({pros:e,cons:s}){return(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("h3",{className:"text-xl font-semibold text-green-700 mb-4 flex items-center gap-2",children:[(0,t.jsx)(l.A,{className:"w-6 h-6"}),"Pros"]}),(0,t.jsx)("ul",{className:"space-y-3",children:e.map((e,s)=>(0,t.jsxs)("li",{className:"flex items-start gap-3",children:[(0,t.jsx)(l.A,{className:"w-5 h-5 text-green-500 flex-shrink-0 mt-0.5"}),(0,t.jsx)("span",{className:"text-gray-700",children:e})]},s))})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("h3",{className:"text-xl font-semibold text-red-700 mb-4 flex items-center gap-2",children:[(0,t.jsx)(r,{className:"w-6 h-6"}),"Cons"]}),(0,t.jsx)("ul",{className:"space-y-3",children:s.map((e,s)=>(0,t.jsxs)("li",{className:"flex items-start gap-3",children:[(0,t.jsx)(r,{className:"w-5 h-5 text-red-500 flex-shrink-0 mt-0.5"}),(0,t.jsx)("span",{className:"text-gray-700",children:e})]},s))})]})]})}},1190:(e,s,a)=>{a.d(s,{AffiliateDisclosure:()=>t});let t=(0,a(2907).registerClientReference)(function(){throw Error("Attempted to call AffiliateDisclosure() from the server but AffiliateDisclosure is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\AffiliateDisclosure.tsx","AffiliateDisclosure")},1227:(e,s,a)=>{a.d(s,{A:()=>t});let t=(0,a(6373).A)("award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]])},3488:(e,s,a)=>{a.d(s,{A:()=>t});let t=(0,a(6373).A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},5200:(e,s,a)=>{a.d(s,{RuntimeCalculator:()=>p});var t=a(687),l=a(3210),r=a(4780);function i({className:e,...s}){return(0,t.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...s})}function n({className:e,...s}){return(0,t.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",e),...s})}var c=a(9667),o=a(2688);let d=(0,o.A)("smartphone",[["rect",{width:"14",height:"20",x:"5",y:"2",rx:"2",ry:"2",key:"1yt0o3"}],["path",{d:"M12 18h.01",key:"mhygvu"}]]),m=(0,o.A)("laptop",[["path",{d:"M18 5a2 2 0 0 1 2 2v8.526a2 2 0 0 0 .212.897l1.068 2.127a1 1 0 0 1-.9 1.45H3.62a1 1 0 0 1-.9-1.45l1.068-2.127A2 2 0 0 0 4 15.526V7a2 2 0 0 1 2-2z",key:"1pdavp"}],["path",{d:"M20.054 15.987H3.946",key:"14rxg9"}]]),h=(0,o.A)("lightbulb",[["path",{d:"M15 14c.2-1 .7-1.7 1.5-2.5 1-.9 1.5-2.2 1.5-3.5A6 6 0 0 0 6 8c0 1 .2 2.2 1.5 3.5.7.7 1.3 1.5 1.5 2.5",key:"1gvzjb"}],["path",{d:"M9 18h6",key:"x1upvd"}],["path",{d:"M10 22h4",key:"ceow96"}]]),x=(0,o.A)("calculator",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",key:"1nb95v"}],["line",{x1:"8",x2:"16",y1:"6",y2:"6",key:"x4nwl0"}],["line",{x1:"16",x2:"16",y1:"14",y2:"18",key:"wjye3r"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M8 14h.01",key:"6423bh"}],["path",{d:"M12 18h.01",key:"mhygvu"}],["path",{d:"M8 18h.01",key:"lrp35t"}]]),u=[{name:"Smartphone",power:5,icon:d},{name:"Laptop",power:65,icon:m},{name:"LED Light",power:10,icon:h}];function p({product:e}){let[s,a]=(0,l.useState)(""),r=s=>(.85*e.capacity_wh/s).toFixed(1);return(0,t.jsx)(i,{children:(0,t.jsxs)(n,{className:"p-6",children:[(0,t.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2",children:[(0,t.jsx)(x,{className:"w-5 h-5"}),"Runtime Calculator"]}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6",children:u.map(e=>{let s=e.icon,a=r(e.power);return(0,t.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4 text-center",children:[(0,t.jsx)(s,{className:"w-8 h-8 text-gray-600 mx-auto mb-2"}),(0,t.jsx)("div",{className:"font-medium text-gray-900",children:e.name}),(0,t.jsxs)("div",{className:"text-sm text-gray-500",children:[e.power,"W"]}),(0,t.jsxs)("div",{className:"text-lg font-bold text-primary-600 mt-2",children:[a," hours"]})]},e.name)})}),(0,t.jsxs)("div",{className:"border-t pt-6",children:[(0,t.jsx)("h4",{className:"font-medium text-gray-900 mb-3",children:"Custom Device"}),(0,t.jsxs)("div",{className:"flex gap-4",children:[(0,t.jsx)(c.p,{type:"number",placeholder:"Power consumption (watts)",value:s,onChange:e=>a(e.target.value),className:"flex-1"}),(0,t.jsx)("div",{className:"flex items-center text-gray-600",children:s&&!isNaN(Number(s))&&Number(s)>0?(0,t.jsxs)("span",{className:"font-medium text-primary-600",children:[r(Number(s))," hours"]}):(0,t.jsx)("span",{children:"Enter watts"})})]})]})]})})}},6371:(e,s,a)=>{a.d(s,{i:()=>r});var t=a(7413),l=a(8963);function r({product:e}){let s=[{label:"Capacity",value:`${e.capacity_wh}Wh`},{label:"Max Output",value:`${e.max_output_w}W`},{label:"Battery Type",value:e.battery_type},{label:"Weight",value:`${e.weight_lbs}lbs`},{label:"AC Outlets",value:e.ac_outlets},{label:"USB-A Ports",value:e.usb_a_ports},{label:"USB-C Ports",value:e.usb_c_ports},{label:"Warranty",value:`${e.warranty_years} years`}].filter(e=>e.value);return(0,t.jsx)(l.Zp,{children:(0,t.jsx)(l.Wu,{className:"p-6",children:(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:s.map((e,s)=>(0,t.jsxs)("div",{className:"flex justify-between py-2 border-b border-gray-100 last:border-0",children:[(0,t.jsx)("span",{className:"text-gray-600",children:e.label}),(0,t.jsx)("span",{className:"font-medium text-gray-900",children:e.value})]},s))})})})}},7539:(e,s,a)=>{a.d(s,{A:()=>t});let t=(0,a(6373).A)("battery",[["rect",{width:"16",height:"10",x:"2",y:"7",rx:"2",ry:"2",key:"1w10f2"}],["line",{x1:"22",x2:"22",y1:"11",y2:"13",key:"4dh1rd"}]])},7584:(e,s,a)=>{a.d(s,{RuntimeCalculator:()=>t});let t=(0,a(2907).registerClientReference)(function(){throw Error("Attempted to call RuntimeCalculator() from the server but RuntimeCalculator is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\offgridflux\\offgridflux-website\\src\\components\\RuntimeCalculator.tsx","RuntimeCalculator")},9466:(e,s,a)=>{a.d(s,{AffiliateDisclosure:()=>m});var t=a(687),l=a(3210),r=a(6834),i=a(9523),n=a(2688);let c=(0,n.A)("info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]]),o=(0,n.A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]]),d=(0,n.A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]);function m(){let[e,s]=(0,l.useState)(!0),[a,n]=(0,l.useState)(!1);return e?(0,t.jsx)("div",{className:"bg-blue-50 border-b border-blue-200 relative",children:(0,t.jsx)("div",{className:"max-w-7xl mx-auto px-4 py-3",children:(0,t.jsxs)("div",{className:"flex items-start gap-3",children:[(0,t.jsx)("div",{className:"flex-shrink-0 mt-0.5",children:(0,t.jsx)(c,{className:"w-5 h-5 text-blue-600"})}),(0,t.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,t.jsx)("div",{className:"flex items-center gap-2 mb-1",children:(0,t.jsx)(r.E,{variant:"outline",className:"text-xs border-blue-300 text-blue-700",children:"FTC Disclosure"})}),(0,t.jsxs)("div",{className:"text-sm text-blue-800",children:[(0,t.jsx)("p",{className:"font-medium mb-1",children:"Affiliate Disclosure: We earn commissions from qualifying purchases."}),a?(0,t.jsxs)("div",{className:"text-blue-700 space-y-2",children:[(0,t.jsx)("p",{children:"OffGridFlux participates in affiliate programs with Amazon, Jackery, Goal Zero, EcoFlow, and other retailers. When you click on links to products and make a purchase, we may receive a commission at no additional cost to you."}),(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"Our Promise:"})," We only recommend products we've personally tested or would use ourselves. Our reviews are based on real-world testing and are not influenced by affiliate relationships. We purchase most products at full retail price for testing."]}),(0,t.jsxs)("p",{children:["Commission earnings help support our independent testing lab and keep our content free for readers. For complete details, see our"," ",(0,t.jsxs)("a",{href:"/affiliate-disclosure",className:"underline hover:no-underline font-medium inline-flex items-center gap-1",children:["full affiliate disclosure policy",(0,t.jsx)(o,{className:"w-3 h-3"})]}),"."]}),(0,t.jsx)("button",{onClick:()=>n(!1),className:"underline hover:no-underline font-medium text-xs",children:"Show less"})]}):(0,t.jsxs)("p",{className:"text-blue-700",children:["As an Amazon Associate and affiliate partner, we earn from qualifying purchases. This doesn't affect our review process or recommendations."," ",(0,t.jsx)("button",{onClick:()=>n(!0),className:"underline hover:no-underline font-medium",children:"Read full disclosure"})]})]})]}),(0,t.jsx)(i.$,{variant:"ghost",size:"sm",onClick:()=>s(!1),className:"flex-shrink-0 h-8 w-8 p-0 text-blue-600 hover:bg-blue-100","aria-label":"Close disclosure",children:(0,t.jsx)(d,{className:"w-4 h-4"})})]})})}):null}}};