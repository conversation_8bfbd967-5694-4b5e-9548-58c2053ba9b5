(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[457],{426:(e,r,t)=>{Promise.resolve().then(t.bind(t,4295))},9946:(e,r,t)=>{"use strict";t.d(r,{A:()=>d});var a=t(2115);let i=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),n=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,r,t)=>t?t.toUpperCase():r.toLowerCase()),o=e=>{let r=n(e);return r.charAt(0).toUpperCase()+r.slice(1)},l=function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return r.filter((e,r,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===r).join(" ").trim()},s=e=>{for(let r in e)if(r.startsWith("aria-")||"role"===r||"title"===r)return!0};var c={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let u=(0,a.forwardRef)((e,r)=>{let{color:t="currentColor",size:i=24,strokeWidth:n=2,absoluteStrokeWidth:o,className:u="",children:d,iconNode:h,...w}=e;return(0,a.createElement)("svg",{ref:r,...c,width:i,height:i,stroke:t,strokeWidth:o?24*Number(n)/Number(i):n,className:l("lucide",u),...!d&&!s(w)&&{"aria-hidden":"true"},...w},[...h.map(e=>{let[r,t]=e;return(0,a.createElement)(r,t)}),...Array.isArray(d)?d:[d]])}),d=(e,r)=>{let t=(0,a.forwardRef)((t,n)=>{let{className:s,...c}=t;return(0,a.createElement)(u,{ref:n,iconNode:r,className:l("lucide-".concat(i(o(e))),"lucide-".concat(e),s),...c})});return t.displayName=o(e),t}}},e=>{var r=r=>e(e.s=r);e.O(0,[800,188,295,441,684,358],()=>r(426)),_N_E=e.O()}]);