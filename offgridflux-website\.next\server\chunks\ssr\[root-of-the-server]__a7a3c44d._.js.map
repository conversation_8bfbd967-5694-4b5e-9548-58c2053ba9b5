{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 64, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/offgridflux/offgridflux-website/src/lib/supabase.ts"], "sourcesContent": ["// OffGridFlux - Supabase Client Configuration\n// Handles database connections and real-time subscriptions\n\nimport { createClient as createSupabaseClient } from '@supabase/supabase-js';\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;\n\nif (!supabaseUrl || !supabaseAnonKey) {\n  throw new Error('Missing Supabase environment variables');\n}\n\n// Create Supabase client\nexport const createClient = () => {\n  return createSupabaseClient(supabaseUrl, supabaseAnonKey, {\n    auth: {\n      persistSession: false, // We don't need auth for this public site\n    },\n  });\n};\n\n// Product-related database functions\nexport const getProducts = async (filters?: {\n  brand?: string;\n  category?: string;\n  minCapacity?: number;\n  maxCapacity?: number;\n  maxPrice?: number;\n}) => {\n  const supabase = createClient();\n  let query = supabase\n    .from('product_specs')\n    .select('*')\n    .eq('availability_status', 'In Stock');\n\n  if (filters?.brand) {\n    query = query.eq('brand', filters.brand);\n  }\n  \n  if (filters?.category) {\n    query = query.eq('category', filters.category);\n  }\n  \n  if (filters?.minCapacity) {\n    query = query.gte('capacity_wh', filters.minCapacity);\n  }\n  \n  if (filters?.maxCapacity) {\n    query = query.lte('capacity_wh', filters.maxCapacity);\n  }\n  \n  if (filters?.maxPrice) {\n    query = query.lte('current_price_usd', filters.maxPrice);\n  }\n\n  return query.order('capacity_wh', { ascending: true });\n};\n\nexport const getProductBySlug = async (slug: string) => {\n  const supabase = createClient();\n  return supabase\n    .from('product_specs')\n    .select('*')\n    .eq('slug', slug)\n    .single();\n};\n\nexport const getFeaturedProducts = async (limit: number = 6) => {\n  const supabase = createClient();\n  return supabase\n    .from('product_specs')\n    .select('*')\n    .eq('availability_status', 'In Stock')\n    .not('offgridflux_rating', 'is', null)\n    .order('offgridflux_rating', { ascending: false })\n    .limit(limit);\n};\n\nexport const getBestValueProducts = async (limit: number = 5) => {\n  const supabase = createClient();\n  const { data: products } = await supabase\n    .from('product_specs')\n    .select('*')\n    .eq('availability_status', 'In Stock')\n    .not('current_price_usd', 'is', null)\n    .not('capacity_wh', 'is', null);\n\n  if (!products) return { data: null, error: 'No products found' };\n\n  // Calculate value score and sort\n  const productsWithValue = products\n    .map(product => ({\n      ...product,\n      valueScore: product.capacity_wh / parseFloat(product.current_price_usd)\n    }))\n    .sort((a, b) => b.valueScore - a.valueScore)\n    .slice(0, limit);\n\n  return { data: productsWithValue, error: null };\n};\n\n// Real-time price updates using Server-Sent Events\nexport const subscribeToPriceUpdates = (callback: (product: Product) => void) => {\n  const supabase = createClient();\n  \n  return supabase\n    .channel('price-updates')\n    .on(\n      'postgres_changes',\n      {\n        event: 'UPDATE',\n        schema: 'public',\n        table: 'product_specs',\n        filter: 'current_price_usd=neq.null'\n      },\n      (payload) => {\n        callback(payload.new as Product);\n      }\n    )\n    .subscribe();\n};\n\n// Search functionality\nexport const searchProducts = async (query: string, limit: number = 10) => {\n  const supabase = createClient();\n  \n  return supabase\n    .from('product_specs')\n    .select('*')\n    .eq('availability_status', 'In Stock')\n    .or(`product_name.ilike.%${query}%,brand.ilike.%${query}%,model.ilike.%${query}%`)\n    .limit(limit);\n};\n\n// Analytics functions\nexport const trackProductView = async (productId: number, userAgent?: string) => {\n  const supabase = createClient();\n  \n  return supabase\n    .from('product_views')\n    .insert({\n      product_id: productId,\n      viewed_at: new Date().toISOString(),\n      user_agent: userAgent\n    });\n};\n\nexport const trackAffiliateClick = async (productId: number, affiliateUrl: string) => {\n  const supabase = createClient();\n  \n  return supabase\n    .from('affiliate_clicks')\n    .insert({\n      product_id: productId,\n      affiliate_url: affiliateUrl,\n      clicked_at: new Date().toISOString()\n    });\n};\n\n// Type definitions for better TypeScript support\nexport interface Product {\n  id: number;\n  created_at: string;\n  updated_at: string;\n  brand: string;\n  model: string;\n  product_name: string;\n  category: string;\n  series?: string;\n  capacity_wh: number;\n  max_output_w: number;\n  peak_output_w?: number;\n  battery_type?: string;\n  weight_lbs?: number;\n  msrp_usd?: number;\n  current_price_usd?: number;\n  discount_percent?: number;\n  ac_outlets?: number;\n  usb_a_ports?: number;\n  usb_c_ports?: number;\n  usb_c_max_w?: number;\n  dc_ports?: number;\n  ups_capability?: boolean;\n  app_control?: boolean;\n  expandable?: boolean;\n  max_expansion_wh?: number;\n  warranty_years?: number;\n  product_image_url?: string;\n  product_gallery_urls?: string[];\n  key_features?: string[];\n  use_cases?: string[];\n  affiliate_url?: string;\n  manufacturer_url?: string;\n  slug: string;\n  meta_title?: string;\n  meta_description?: string;\n  keywords?: string[];\n  tested_by_offgridflux?: boolean;\n  offgridflux_rating?: number;\n  pros?: string[];\n  cons?: string[];\n  availability_status: string;\n  runtime_smartphone_hours?: number;\n  runtime_laptop_hours?: number;\n  runtime_mini_fridge_hours?: number;\n  runtime_cpap_hours?: number;\n  runtime_led_lights_hours?: number;\n}\n\nexport interface ProductFilters {\n  brand?: string;\n  category?: string;\n  minCapacity?: number;\n  maxCapacity?: number;\n  maxPrice?: number;\n  minRating?: number;\n  features?: string[];\n}\n"], "names": [], "mappings": "AAAA,8CAA8C;AAC9C,2DAA2D;;;;;;;;;;;;AAE3D;;AAEA,MAAM;AACN,MAAM;AAEN,uCAAsC;;AAEtC;AAGO,MAAM,eAAe;IAC1B,OAAO,CAAA,GAAA,uLAAA,CAAA,eAAoB,AAAD,EAAE,aAAa,iBAAiB;QACxD,MAAM;YACJ,gBAAgB;QAClB;IACF;AACF;AAGO,MAAM,cAAc,OAAO;IAOhC,MAAM,WAAW;IACjB,IAAI,QAAQ,SACT,IAAI,CAAC,iBACL,MAAM,CAAC,KACP,EAAE,CAAC,uBAAuB;IAE7B,IAAI,SAAS,OAAO;QAClB,QAAQ,MAAM,EAAE,CAAC,SAAS,QAAQ,KAAK;IACzC;IAEA,IAAI,SAAS,UAAU;QACrB,QAAQ,MAAM,EAAE,CAAC,YAAY,QAAQ,QAAQ;IAC/C;IAEA,IAAI,SAAS,aAAa;QACxB,QAAQ,MAAM,GAAG,CAAC,eAAe,QAAQ,WAAW;IACtD;IAEA,IAAI,SAAS,aAAa;QACxB,QAAQ,MAAM,GAAG,CAAC,eAAe,QAAQ,WAAW;IACtD;IAEA,IAAI,SAAS,UAAU;QACrB,QAAQ,MAAM,GAAG,CAAC,qBAAqB,QAAQ,QAAQ;IACzD;IAEA,OAAO,MAAM,KAAK,CAAC,eAAe;QAAE,WAAW;IAAK;AACtD;AAEO,MAAM,mBAAmB,OAAO;IACrC,MAAM,WAAW;IACjB,OAAO,SACJ,IAAI,CAAC,iBACL,MAAM,CAAC,KACP,EAAE,CAAC,QAAQ,MACX,MAAM;AACX;AAEO,MAAM,sBAAsB,OAAO,QAAgB,CAAC;IACzD,MAAM,WAAW;IACjB,OAAO,SACJ,IAAI,CAAC,iBACL,MAAM,CAAC,KACP,EAAE,CAAC,uBAAuB,YAC1B,GAAG,CAAC,sBAAsB,MAAM,MAChC,KAAK,CAAC,sBAAsB;QAAE,WAAW;IAAM,GAC/C,KAAK,CAAC;AACX;AAEO,MAAM,uBAAuB,OAAO,QAAgB,CAAC;IAC1D,MAAM,WAAW;IACjB,MAAM,EAAE,MAAM,QAAQ,EAAE,GAAG,MAAM,SAC9B,IAAI,CAAC,iBACL,MAAM,CAAC,KACP,EAAE,CAAC,uBAAuB,YAC1B,GAAG,CAAC,qBAAqB,MAAM,MAC/B,GAAG,CAAC,eAAe,MAAM;IAE5B,IAAI,CAAC,UAAU,OAAO;QAAE,MAAM;QAAM,OAAO;IAAoB;IAE/D,iCAAiC;IACjC,MAAM,oBAAoB,SACvB,GAAG,CAAC,CAAA,UAAW,CAAC;YACf,GAAG,OAAO;YACV,YAAY,QAAQ,WAAW,GAAG,WAAW,QAAQ,iBAAiB;QACxE,CAAC,GACA,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,UAAU,GAAG,EAAE,UAAU,EAC1C,KAAK,CAAC,GAAG;IAEZ,OAAO;QAAE,MAAM;QAAmB,OAAO;IAAK;AAChD;AAGO,MAAM,0BAA0B,CAAC;IACtC,MAAM,WAAW;IAEjB,OAAO,SACJ,OAAO,CAAC,iBACR,EAAE,CACD,oBACA;QACE,OAAO;QACP,QAAQ;QACR,OAAO;QACP,QAAQ;IACV,GACA,CAAC;QACC,SAAS,QAAQ,GAAG;IACtB,GAED,SAAS;AACd;AAGO,MAAM,iBAAiB,OAAO,OAAe,QAAgB,EAAE;IACpE,MAAM,WAAW;IAEjB,OAAO,SACJ,IAAI,CAAC,iBACL,MAAM,CAAC,KACP,EAAE,CAAC,uBAAuB,YAC1B,EAAE,CAAC,CAAC,oBAAoB,EAAE,MAAM,eAAe,EAAE,MAAM,eAAe,EAAE,MAAM,CAAC,CAAC,EAChF,KAAK,CAAC;AACX;AAGO,MAAM,mBAAmB,OAAO,WAAmB;IACxD,MAAM,WAAW;IAEjB,OAAO,SACJ,IAAI,CAAC,iBACL,MAAM,CAAC;QACN,YAAY;QACZ,WAAW,IAAI,OAAO,WAAW;QACjC,YAAY;IACd;AACJ;AAEO,MAAM,sBAAsB,OAAO,WAAmB;IAC3D,MAAM,WAAW;IAEjB,OAAO,SACJ,IAAI,CAAC,oBACL,MAAM,CAAC;QACN,YAAY;QACZ,eAAe;QACf,YAAY,IAAI,OAAO,WAAW;IACpC;AACJ", "debugId": null}}, {"offset": {"line": 177, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/offgridflux/offgridflux-website/src/components/ProductCard.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ProductCard = registerClientReference(\n    function() { throw new Error(\"Attempted to call ProductCard() from the server but ProductCard is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ProductCard.tsx <module evaluation>\",\n    \"ProductCard\",\n);\nexport const ProductCardSkeleton = registerClientReference(\n    function() { throw new Error(\"Attempted to call ProductCardSkeleton() from the server but ProductCardSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ProductCard.tsx <module evaluation>\",\n    \"ProductCardSkeleton\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,cAAc,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,gEACA;AAEG,MAAM,sBAAsB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACrD;IAAa,MAAM,IAAI,MAAM;AAAsP,GACnR,gEACA", "debugId": null}}, {"offset": {"line": 195, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/offgridflux/offgridflux-website/src/components/ProductCard.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ProductCard = registerClientReference(\n    function() { throw new Error(\"Attempted to call ProductCard() from the server but ProductCard is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ProductCard.tsx\",\n    \"ProductCard\",\n);\nexport const ProductCardSkeleton = registerClientReference(\n    function() { throw new Error(\"Attempted to call ProductCardSkeleton() from the server but ProductCardSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ProductCard.tsx\",\n    \"ProductCardSkeleton\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,cAAc,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,4CACA;AAEG,MAAM,sBAAsB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACrD;IAAa,MAAM,IAAI,MAAM;AAAsP,GACnR,4CACA", "debugId": null}}, {"offset": {"line": 213, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 223, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/offgridflux/offgridflux-website/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 239, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/offgridflux/offgridflux-website/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 285, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/offgridflux/offgridflux-website/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 342, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/offgridflux/offgridflux-website/src/components/CategoryHero.tsx"], "sourcesContent": ["// OffGridFlux - Category Hero Component\n// Hero section for category pages\n\nimport { Badge } from '@/components/ui/badge';\nimport { Button } from '@/components/ui/button';\nimport { Zap, Filter, Grid } from 'lucide-react';\n\ninterface CategoryHeroProps {\n  title: string;\n  description: string;\n  productCount: number;\n}\n\nexport function CategoryHero({ title, description, productCount }: CategoryHeroProps) {\n  return (\n    <section className=\"bg-gradient-to-r from-gray-900 to-gray-800 text-white py-16\">\n      <div className=\"max-w-7xl mx-auto px-4\">\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\">\n          \n          {/* Left Column - Content */}\n          <div>\n            <Badge className=\"mb-4 bg-white/10 text-white border-white/20\">\n              <Grid className=\"w-4 h-4 mr-2\" />\n              Product Category\n            </Badge>\n            \n            <h1 className=\"text-4xl md:text-5xl font-bold mb-6 leading-tight\">\n              {title}\n            </h1>\n            \n            <p className=\"text-xl text-gray-300 mb-8 leading-relaxed\">\n              {description}\n            </p>\n            \n            <div className=\"flex items-center gap-6 mb-8\">\n              <div className=\"flex items-center gap-2 text-gray-300\">\n                <Zap className=\"w-5 h-5\" />\n                <span>{productCount} Products</span>\n              </div>\n              <div className=\"flex items-center gap-2 text-gray-300\">\n                <Filter className=\"w-5 h-5\" />\n                <span>Expert Tested</span>\n              </div>\n            </div>\n            \n            <div className=\"flex flex-col sm:flex-row gap-4\">\n              <Button className=\"bg-primary-600 hover:bg-primary-700\">\n                View All Products\n              </Button>\n              <Button variant=\"outline\" className=\"border-white text-white hover:bg-white hover:text-gray-900\">\n                Compare Products\n              </Button>\n            </div>\n          </div>\n          \n          {/* Right Column - Visual */}\n          <div className=\"relative\">\n            <div className=\"bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20\">\n              <div className=\"aspect-square bg-gradient-to-br from-white/20 to-white/5 rounded-xl flex items-center justify-center\">\n                <Zap className=\"w-24 h-24 text-white/60\" />\n              </div>\n            </div>\n            \n            {/* Background Glow */}\n            <div className=\"absolute inset-0 bg-gradient-to-r from-primary-400/20 to-primary-600/20 rounded-2xl blur-3xl -z-10 transform scale-110\"></div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": "AAAA,wCAAwC;AACxC,kCAAkC;;;;;AAElC;AACA;AACA;AAAA;AAAA;;;;;AAQO,SAAS,aAAa,EAAE,KAAK,EAAE,WAAW,EAAE,YAAY,EAAqB;IAClF,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAGb,8OAAC;;0CACC,8OAAC,iIAAA,CAAA,QAAK;gCAAC,WAAU;;kDACf,8OAAC,yMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAInC,8OAAC;gCAAG,WAAU;0CACX;;;;;;0CAGH,8OAAC;gCAAE,WAAU;0CACV;;;;;;0CAGH,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,gMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;0DACf,8OAAC;;oDAAM;oDAAa;;;;;;;;;;;;;kDAEtB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,8OAAC;0DAAK;;;;;;;;;;;;;;;;;;0CAIV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCAAC,WAAU;kDAAsC;;;;;;kDAGxD,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,WAAU;kDAA6D;;;;;;;;;;;;;;;;;;kCAOrG,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,gMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;;;;;;;;;;;0CAKnB,8OAAC;gCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM3B", "debugId": null}}, {"offset": {"line": 550, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/offgridflux/offgridflux-website/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 647, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/offgridflux/offgridflux-website/src/components/ComparisonTable.tsx"], "sourcesContent": ["// OffGridFlux - Comparison Table Component\n// Displays product comparison in table format\n\nimport { Card, CardContent } from '@/components/ui/card';\nimport { Badge } from '@/components/ui/badge';\nimport { Button } from '@/components/ui/button';\nimport { Product } from '@/lib/supabase';\nimport { ExternalLink, Star } from 'lucide-react';\n\ninterface ComparisonTableProps {\n  products: Product[];\n}\n\nexport function ComparisonTable({ products }: ComparisonTableProps) {\n  if (products.length === 0) return null;\n\n  return (\n    <Card className=\"overflow-hidden\">\n      <CardContent className=\"p-0\">\n        <div className=\"overflow-x-auto\">\n          <table className=\"w-full\">\n            <thead className=\"bg-gray-50\">\n              <tr>\n                <th className=\"text-left p-4 font-semibold text-gray-900 min-w-[200px]\">\n                  Product\n                </th>\n                <th className=\"text-center p-4 font-semibold text-gray-900 min-w-[120px]\">\n                  Capacity\n                </th>\n                <th className=\"text-center p-4 font-semibold text-gray-900 min-w-[120px]\">\n                  Output\n                </th>\n                <th className=\"text-center p-4 font-semibold text-gray-900 min-w-[100px]\">\n                  Weight\n                </th>\n                <th className=\"text-center p-4 font-semibold text-gray-900 min-w-[120px]\">\n                  Price\n                </th>\n                <th className=\"text-center p-4 font-semibold text-gray-900 min-w-[100px]\">\n                  Rating\n                </th>\n                <th className=\"text-center p-4 font-semibold text-gray-900 min-w-[120px]\">\n                  Action\n                </th>\n              </tr>\n            </thead>\n            <tbody>\n              {products.map((product, index) => (\n                <tr key={product.id} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>\n                  {/* Product */}\n                  <td className=\"p-4\">\n                    <div className=\"flex items-center gap-3\">\n                      <div className=\"w-12 h-12 bg-gray-200 rounded-lg flex items-center justify-center\">\n                        <span className=\"text-xs text-gray-500\">{product.brand[0]}</span>\n                      </div>\n                      <div>\n                        <div className=\"font-medium text-gray-900 text-sm\">\n                          {product.brand} {product.model}\n                        </div>\n                        <div className=\"text-xs text-gray-500\">\n                          {product.battery_type}\n                        </div>\n                      </div>\n                    </div>\n                  </td>\n                  \n                  {/* Capacity */}\n                  <td className=\"p-4 text-center\">\n                    <div className=\"font-medium text-gray-900\">\n                      {product.capacity_wh}Wh\n                    </div>\n                  </td>\n                  \n                  {/* Output */}\n                  <td className=\"p-4 text-center\">\n                    <div className=\"font-medium text-gray-900\">\n                      {product.max_output_w}W\n                    </div>\n                    {product.peak_output_w && (\n                      <div className=\"text-xs text-gray-500\">\n                        {product.peak_output_w}W peak\n                      </div>\n                    )}\n                  </td>\n                  \n                  {/* Weight */}\n                  <td className=\"p-4 text-center\">\n                    <div className=\"font-medium text-gray-900\">\n                      {product.weight_lbs}lbs\n                    </div>\n                  </td>\n                  \n                  {/* Price */}\n                  <td className=\"p-4 text-center\">\n                    <div className=\"font-medium text-gray-900\">\n                      ${product.current_price_usd}\n                    </div>\n                    {product.discount_percent && (\n                      <Badge variant=\"outline\" className=\"text-xs mt-1 border-red-200 text-red-700\">\n                        {product.discount_percent}% off\n                      </Badge>\n                    )}\n                  </td>\n                  \n                  {/* Rating */}\n                  <td className=\"p-4 text-center\">\n                    {product.offgridflux_rating ? (\n                      <div className=\"flex items-center justify-center gap-1\">\n                        <Star className=\"w-4 h-4 text-yellow-400 fill-current\" />\n                        <span className=\"font-medium text-gray-900\">\n                          {product.offgridflux_rating}\n                        </span>\n                      </div>\n                    ) : (\n                      <span className=\"text-gray-400 text-sm\">Not rated</span>\n                    )}\n                  </td>\n                  \n                  {/* Action */}\n                  <td className=\"p-4 text-center\">\n                    <div className=\"flex flex-col gap-2\">\n                      <Button size=\"sm\" asChild>\n                        <a href={`/product/${product.slug}`}>\n                          Review\n                        </a>\n                      </Button>\n                      {product.affiliate_url && (\n                        <Button size=\"sm\" variant=\"outline\" asChild>\n                          <a \n                            href={product.affiliate_url}\n                            target=\"_blank\"\n                            rel=\"noopener noreferrer\"\n                            className=\"flex items-center gap-1\"\n                          >\n                            <span>Buy</span>\n                            <ExternalLink className=\"w-3 h-3\" />\n                          </a>\n                        </Button>\n                      )}\n                    </div>\n                  </td>\n                </tr>\n              ))}\n            </tbody>\n          </table>\n        </div>\n      </CardContent>\n    </Card>\n  );\n}\n"], "names": [], "mappings": "AAAA,2CAA2C;AAC3C,8CAA8C;;;;;AAE9C;AACA;AACA;AAEA;AAAA;;;;;;AAMO,SAAS,gBAAgB,EAAE,QAAQ,EAAwB;IAChE,IAAI,SAAS,MAAM,KAAK,GAAG,OAAO;IAElC,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAU;kBACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;YAAC,WAAU;sBACrB,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAM,WAAU;;sCACf,8OAAC;4BAAM,WAAU;sCACf,cAAA,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAA0D;;;;;;kDAGxE,8OAAC;wCAAG,WAAU;kDAA4D;;;;;;kDAG1E,8OAAC;wCAAG,WAAU;kDAA4D;;;;;;kDAG1E,8OAAC;wCAAG,WAAU;kDAA4D;;;;;;kDAG1E,8OAAC;wCAAG,WAAU;kDAA4D;;;;;;kDAG1E,8OAAC;wCAAG,WAAU;kDAA4D;;;;;;kDAG1E,8OAAC;wCAAG,WAAU;kDAA4D;;;;;;;;;;;;;;;;;sCAK9E,8OAAC;sCACE,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC;oCAAoB,WAAW,QAAQ,MAAM,IAAI,aAAa;;sDAE7D,8OAAC;4CAAG,WAAU;sDACZ,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAK,WAAU;sEAAyB,QAAQ,KAAK,CAAC,EAAE;;;;;;;;;;;kEAE3D,8OAAC;;0EACC,8OAAC;gEAAI,WAAU;;oEACZ,QAAQ,KAAK;oEAAC;oEAAE,QAAQ,KAAK;;;;;;;0EAEhC,8OAAC;gEAAI,WAAU;0EACZ,QAAQ,YAAY;;;;;;;;;;;;;;;;;;;;;;;sDAO7B,8OAAC;4CAAG,WAAU;sDACZ,cAAA,8OAAC;gDAAI,WAAU;;oDACZ,QAAQ,WAAW;oDAAC;;;;;;;;;;;;sDAKzB,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAI,WAAU;;wDACZ,QAAQ,YAAY;wDAAC;;;;;;;gDAEvB,QAAQ,aAAa,kBACpB,8OAAC;oDAAI,WAAU;;wDACZ,QAAQ,aAAa;wDAAC;;;;;;;;;;;;;sDAM7B,8OAAC;4CAAG,WAAU;sDACZ,cAAA,8OAAC;gDAAI,WAAU;;oDACZ,QAAQ,UAAU;oDAAC;;;;;;;;;;;;sDAKxB,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAI,WAAU;;wDAA4B;wDACvC,QAAQ,iBAAiB;;;;;;;gDAE5B,QAAQ,gBAAgB,kBACvB,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAU,WAAU;;wDAChC,QAAQ,gBAAgB;wDAAC;;;;;;;;;;;;;sDAMhC,8OAAC;4CAAG,WAAU;sDACX,QAAQ,kBAAkB,iBACzB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAChB,8OAAC;wDAAK,WAAU;kEACb,QAAQ,kBAAkB;;;;;;;;;;;qEAI/B,8OAAC;gDAAK,WAAU;0DAAwB;;;;;;;;;;;sDAK5C,8OAAC;4CAAG,WAAU;sDACZ,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kIAAA,CAAA,SAAM;wDAAC,MAAK;wDAAK,OAAO;kEACvB,cAAA,8OAAC;4DAAE,MAAM,CAAC,SAAS,EAAE,QAAQ,IAAI,EAAE;sEAAE;;;;;;;;;;;oDAItC,QAAQ,aAAa,kBACpB,8OAAC,kIAAA,CAAA,SAAM;wDAAC,MAAK;wDAAK,SAAQ;wDAAU,OAAO;kEACzC,cAAA,8OAAC;4DACC,MAAM,QAAQ,aAAa;4DAC3B,QAAO;4DACP,KAAI;4DACJ,WAAU;;8EAEV,8OAAC;8EAAK;;;;;;8EACN,8OAAC,sNAAA,CAAA,eAAY;oEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;mCAvF3B,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqGnC", "debugId": null}}, {"offset": {"line": 1051, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/offgridflux/offgridflux-website/src/app/category/%5Bslug%5D/page.tsx"], "sourcesContent": ["// OffGridFlux - Category Page Component\n// Dynamic route for category landing pages (e.g., /category/solar-generators)\n\nimport { Metadata } from 'next';\nimport { notFound } from 'next/navigation';\nimport { createClient, Product } from '@/lib/supabase';\nimport { ProductCard } from '@/components/ProductCard';\nimport { CategoryHero } from '@/components/CategoryHero';\nimport { ComparisonTable } from '@/components/ComparisonTable';\n\ninterface CategoryPageProps {\n  params: Promise<{\n    slug: string;\n  }>;\n}\n\n// Category mapping for SEO and display\nconst categoryMap: Record<string, { \n  title: string; \n  description: string; \n  category: string;\n  keywords: string[];\n}> = {\n  'solar-generators': {\n    title: 'Best Solar Generators 2025',\n    description: 'Compare the top portable solar generators from Jackery, Goal Zero, and EcoFlow. Expert reviews, real-world testing, and buying guides.',\n    category: 'Portable Power Station',\n    keywords: ['solar generator', 'portable power station', 'off grid power', 'backup power']\n  },\n  'portable-power-stations': {\n    title: 'Best Portable Power Stations 2025',\n    description: 'Find the perfect portable power station for camping, emergencies, and off-grid living. Compare capacity, features, and prices.',\n    category: 'Portable Power Station',\n    keywords: ['portable power station', 'battery pack', 'power bank', 'emergency power']\n  },\n  'home-battery-systems': {\n    title: 'Best Home Battery Systems 2025',\n    description: 'Whole home backup power solutions for grid independence. Compare expandable battery systems and installation options.',\n    category: 'Home Battery System',\n    keywords: ['home battery', 'whole home backup', 'grid independence', 'solar battery']\n  }\n};\n\nexport async function generateMetadata({ params }: CategoryPageProps): Promise<Metadata> {\n  const resolvedParams = await params;\n  const categoryInfo = categoryMap[resolvedParams.slug];\n  \n  if (!categoryInfo) {\n    return {\n      title: 'Category Not Found - OffGridFlux',\n      description: 'The requested category could not be found.'\n    };\n  }\n\n  return {\n    title: `${categoryInfo.title} | OffGridFlux`,\n    description: categoryInfo.description,\n    keywords: categoryInfo.keywords.join(', '),\n    openGraph: {\n      title: `${categoryInfo.title} | OffGridFlux`,\n      description: categoryInfo.description,\n      type: 'website',\n      url: `https://offgridflux.com/category/${resolvedParams.slug}`,\n    },\n    twitter: {\n      card: 'summary_large_image',\n      title: `${categoryInfo.title} | OffGridFlux`,\n      description: categoryInfo.description,\n    }\n  };\n}\n\nexport default async function CategoryPage({ params }: CategoryPageProps) {\n  const resolvedParams = await params;\n  const categoryInfo = categoryMap[resolvedParams.slug];\n  \n  if (!categoryInfo) {\n    notFound();\n  }\n\n  // Fetch products for this category\n  const supabase = createClient();\n  const { data: products, error } = await supabase\n    .from('product_specs')\n    .select('*')\n    .eq('category', categoryInfo.category)\n    .eq('availability_status', 'In Stock')\n    .order('capacity_wh', { ascending: true });\n\n  if (error) {\n    console.error('Error fetching products:', error);\n    return <div>Error loading products</div>;\n  }\n\n  // Group products by brand for comparison\n  const productsByBrand = products?.reduce((acc, product) => {\n    if (!acc[product.brand]) {\n      acc[product.brand] = [];\n    }\n    acc[product.brand].push(product);\n    return acc;\n  }, {} as Record<string, Product[]>) || {};\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Category Hero Section */}\n      <CategoryHero \n        title={categoryInfo.title}\n        description={categoryInfo.description}\n        productCount={products?.length || 0}\n      />\n\n      {/* Products Grid */}\n      <section className=\"py-12 px-4 max-w-7xl mx-auto\">\n        <div className=\"mb-8\">\n          <h2 className=\"text-2xl font-bold text-gray-900 mb-4\">\n            Top {categoryInfo.title.replace('Best ', '').replace(' 2025', '')}\n          </h2>\n          <p className=\"text-gray-600\">\n            Compare specifications, prices, and features to find the perfect power solution.\n          </p>\n        </div>\n\n        {/* Product Cards Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12\">\n          {products?.map((product) => (\n            <ProductCard key={product.id} product={product} />\n          ))}\n        </div>\n\n        {/* Comparison Table */}\n        {products && products.length > 0 && (\n          <div className=\"mb-12\">\n            <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">\n              Detailed Comparison\n            </h2>\n            <ComparisonTable products={products} />\n          </div>\n        )}\n\n        {/* Brand Sections */}\n        {Object.entries(productsByBrand).map(([brand, brandProducts]) => (\n          <div key={brand} className=\"mb-12\">\n            <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">\n              {brand} {categoryInfo.title.replace('Best ', '').replace(' 2025', '')}\n            </h2>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n              {(brandProducts as Product[]).map((product) => (\n                <ProductCard key={product.id} product={product} />\n              ))}\n            </div>\n          </div>\n        ))}\n      </section>\n\n      {/* FAQ Section */}\n      <section className=\"py-12 bg-white\">\n        <div className=\"max-w-4xl mx-auto px-4\">\n          <h2 className=\"text-2xl font-bold text-gray-900 mb-8 text-center\">\n            Frequently Asked Questions\n          </h2>\n          {/* FAQ content will be added based on category */}\n          <div className=\"space-y-6\">\n            <div className=\"border-b border-gray-200 pb-6\">\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">\n                What size {categoryInfo.title.toLowerCase().replace('best ', '').replace(' 2025', '')} do I need?\n              </h3>\n              <p className=\"text-gray-600\">\n                The size depends on your power needs. Calculate your daily energy consumption and choose a capacity that provides 1-2 days of backup power.\n              </p>\n            </div>\n          </div>\n        </div>\n      </section>\n    </div>\n  );\n}\n\n// Generate static params for known categories\nexport async function generateStaticParams() {\n  return Object.keys(categoryMap).map((slug) => ({\n    slug,\n  }));\n}\n"], "names": [], "mappings": "AAAA,wCAAwC;AACxC,8EAA8E;;;;;;;AAG9E;AAAA;AACA;AACA;AACA;AACA;;;;;;;AAQA,uCAAuC;AACvC,MAAM,cAKD;IACH,oBAAoB;QAClB,OAAO;QACP,aAAa;QACb,UAAU;QACV,UAAU;YAAC;YAAmB;YAA0B;YAAkB;SAAe;IAC3F;IACA,2BAA2B;QACzB,OAAO;QACP,aAAa;QACb,UAAU;QACV,UAAU;YAAC;YAA0B;YAAgB;YAAc;SAAkB;IACvF;IACA,wBAAwB;QACtB,OAAO;QACP,aAAa;QACb,UAAU;QACV,UAAU;YAAC;YAAgB;YAAqB;YAAqB;SAAgB;IACvF;AACF;AAEO,eAAe,iBAAiB,EAAE,MAAM,EAAqB;IAClE,MAAM,iBAAiB,MAAM;IAC7B,MAAM,eAAe,WAAW,CAAC,eAAe,IAAI,CAAC;IAErD,IAAI,CAAC,cAAc;QACjB,OAAO;YACL,OAAO;YACP,aAAa;QACf;IACF;IAEA,OAAO;QACL,OAAO,GAAG,aAAa,KAAK,CAAC,cAAc,CAAC;QAC5C,aAAa,aAAa,WAAW;QACrC,UAAU,aAAa,QAAQ,CAAC,IAAI,CAAC;QACrC,WAAW;YACT,OAAO,GAAG,aAAa,KAAK,CAAC,cAAc,CAAC;YAC5C,aAAa,aAAa,WAAW;YACrC,MAAM;YACN,KAAK,CAAC,iCAAiC,EAAE,eAAe,IAAI,EAAE;QAChE;QACA,SAAS;YACP,MAAM;YACN,OAAO,GAAG,aAAa,KAAK,CAAC,cAAc,CAAC;YAC5C,aAAa,aAAa,WAAW;QACvC;IACF;AACF;AAEe,eAAe,aAAa,EAAE,MAAM,EAAqB;IACtE,MAAM,iBAAiB,MAAM;IAC7B,MAAM,eAAe,WAAW,CAAC,eAAe,IAAI,CAAC;IAErD,IAAI,CAAC,cAAc;QACjB,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD;IACT;IAEA,mCAAmC;IACnC,MAAM,WAAW,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD;IAC5B,MAAM,EAAE,MAAM,QAAQ,EAAE,KAAK,EAAE,GAAG,MAAM,SACrC,IAAI,CAAC,iBACL,MAAM,CAAC,KACP,EAAE,CAAC,YAAY,aAAa,QAAQ,EACpC,EAAE,CAAC,uBAAuB,YAC1B,KAAK,CAAC,eAAe;QAAE,WAAW;IAAK;IAE1C,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,qBAAO,8OAAC;sBAAI;;;;;;IACd;IAEA,yCAAyC;IACzC,MAAM,kBAAkB,UAAU,OAAO,CAAC,KAAK;QAC7C,IAAI,CAAC,GAAG,CAAC,QAAQ,KAAK,CAAC,EAAE;YACvB,GAAG,CAAC,QAAQ,KAAK,CAAC,GAAG,EAAE;QACzB;QACA,GAAG,CAAC,QAAQ,KAAK,CAAC,CAAC,IAAI,CAAC;QACxB,OAAO;IACT,GAAG,CAAC,MAAmC,CAAC;IAExC,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,kIAAA,CAAA,eAAY;gBACX,OAAO,aAAa,KAAK;gBACzB,aAAa,aAAa,WAAW;gBACrC,cAAc,UAAU,UAAU;;;;;;0BAIpC,8OAAC;gBAAQ,WAAU;;kCACjB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;oCAAwC;oCAC/C,aAAa,KAAK,CAAC,OAAO,CAAC,SAAS,IAAI,OAAO,CAAC,SAAS;;;;;;;0CAEhE,8OAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;kCAM/B,8OAAC;wBAAI,WAAU;kCACZ,UAAU,IAAI,CAAC,wBACd,8OAAC,iIAAA,CAAA,cAAW;gCAAkB,SAAS;+BAArB,QAAQ,EAAE;;;;;;;;;;oBAK/B,YAAY,SAAS,MAAM,GAAG,mBAC7B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAwC;;;;;;0CAGtD,8OAAC,qIAAA,CAAA,kBAAe;gCAAC,UAAU;;;;;;;;;;;;oBAK9B,OAAO,OAAO,CAAC,iBAAiB,GAAG,CAAC,CAAC,CAAC,OAAO,cAAc,iBAC1D,8OAAC;4BAAgB,WAAU;;8CACzB,8OAAC;oCAAG,WAAU;;wCACX;wCAAM;wCAAE,aAAa,KAAK,CAAC,OAAO,CAAC,SAAS,IAAI,OAAO,CAAC,SAAS;;;;;;;8CAEpE,8OAAC;oCAAI,WAAU;8CACZ,AAAC,cAA4B,GAAG,CAAC,CAAC,wBACjC,8OAAC,iIAAA,CAAA,cAAW;4CAAkB,SAAS;2CAArB,QAAQ,EAAE;;;;;;;;;;;2BANxB;;;;;;;;;;;0BAcd,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAoD;;;;;;sCAIlE,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;;4CAA2C;4CAC5C,aAAa,KAAK,CAAC,WAAW,GAAG,OAAO,CAAC,SAAS,IAAI,OAAO,CAAC,SAAS;4CAAI;;;;;;;kDAExF,8OAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS3C;AAGO,eAAe;IACpB,OAAO,OAAO,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC,OAAS,CAAC;YAC7C;QACF,CAAC;AACH", "debugId": null}}]}