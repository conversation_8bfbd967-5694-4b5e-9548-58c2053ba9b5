// OffGridFlux - Analytics Integration
// Google Analytics 4 and conversion tracking

declare global {
  interface Window {
    gtag: (...args: unknown[]) => void;
    dataLayer: unknown[];
  }
}

// Google Analytics 4 Configuration
export const GA_MEASUREMENT_ID = process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID || 'G-XXXXXXXXXX';

// Initialize Google Analytics
export const initGA = () => {
  if (typeof window === 'undefined') return;
  
  // Load Google Analytics script
  const script = document.createElement('script');
  script.src = `https://www.googletagmanager.com/gtag/js?id=${GA_MEASUREMENT_ID}`;
  script.async = true;
  document.head.appendChild(script);
  
  // Initialize dataLayer and gtag
  window.dataLayer = window.dataLayer || [];
  window.gtag = function gtag(...args: unknown[]) {
    window.dataLayer.push(args);
  };
  
  window.gtag('js', new Date());
  window.gtag('config', GA_MEASUREMENT_ID, {
    page_title: document.title,
    page_location: window.location.href,
  });
};

// Track page views
export const trackPageView = (url: string, title?: string) => {
  if (typeof window === 'undefined' || !window.gtag) return;
  
  window.gtag('config', GA_MEASUREMENT_ID, {
    page_title: title || document.title,
    page_location: url,
  });
};

// Track custom events
export const trackEvent = (
  action: string,
  category: string,
  label?: string,
  value?: number,
  customParameters?: Record<string, unknown>
) => {
  if (typeof window === 'undefined' || !window.gtag) return;
  
  window.gtag('event', action, {
    event_category: category,
    event_label: label,
    value: value,
    ...customParameters,
  });
};

// E-commerce and affiliate tracking
export const trackAffiliateClick = (
  productName: string,
  productId: string,
  brand: string,
  price: number,
  url: string
) => {
  trackEvent('affiliate_click', 'ecommerce', productName, price, {
    product_id: productId,
    product_name: productName,
    product_brand: brand,
    product_price: price,
    affiliate_url: url,
    currency: 'USD',
  });
};

// Track product views
export const trackProductView = (
  productName: string,
  productId: string,
  brand: string,
  price: number,
  category: string
) => {
  if (typeof window === 'undefined' || !window.gtag) return;
  
  window.gtag('event', 'view_item', {
    currency: 'USD',
    value: price,
    items: [
      {
        item_id: productId,
        item_name: productName,
        item_brand: brand,
        item_category: category,
        price: price,
        quantity: 1,
      },
    ],
  });
};

// Track newsletter signups
export const trackNewsletterSignup = (source: string) => {
  trackEvent('newsletter_signup', 'engagement', source, undefined, {
    signup_source: source,
  });
};

// Track search queries
export const trackSearch = (searchTerm: string, resultsCount: number) => {
  trackEvent('search', 'engagement', searchTerm, resultsCount, {
    search_term: searchTerm,
    results_count: resultsCount,
  });
};

// Track content engagement
export const trackContentEngagement = (
  contentType: string,
  contentId: string,
  engagementType: string,
  value?: number
) => {
  trackEvent(engagementType, 'content', contentId, value, {
    content_type: contentType,
    content_id: contentId,
  });
};

// Track scroll depth
export const trackScrollDepth = (percentage: number, page: string) => {
  trackEvent('scroll_depth', 'engagement', page, percentage, {
    scroll_percentage: percentage,
    page_path: page,
  });
};

// Track time on page
export const trackTimeOnPage = (timeInSeconds: number, page: string) => {
  trackEvent('time_on_page', 'engagement', page, timeInSeconds, {
    time_seconds: timeInSeconds,
    page_path: page,
  });
};

// Enhanced E-commerce tracking for affiliate conversions
export const trackPurchaseIntent = (
  productName: string,
  productId: string,
  brand: string,
  price: number,
  step: 'view_review' | 'click_affiliate' | 'add_to_cart'
) => {
  const eventName = step === 'view_review' ? 'view_item' : 
                   step === 'click_affiliate' ? 'select_item' : 'add_to_cart';
  
  if (typeof window === 'undefined' || !window.gtag) return;
  
  window.gtag('event', eventName, {
    currency: 'USD',
    value: price,
    items: [
      {
        item_id: productId,
        item_name: productName,
        item_brand: brand,
        item_category: 'Portable Power Station',
        price: price,
        quantity: 1,
      },
    ],
  });
};

// Track user preferences and behavior
export const trackUserPreference = (
  preferenceType: string,
  preferenceValue: string
) => {
  trackEvent('user_preference', 'personalization', preferenceType, undefined, {
    preference_type: preferenceType,
    preference_value: preferenceValue,
  });
};

// Conversion tracking for different funnel stages
export const trackConversionFunnel = (
  stage: 'awareness' | 'consideration' | 'decision' | 'action',
  source: string,
  productId?: string
) => {
  trackEvent('conversion_funnel', 'ecommerce', stage, undefined, {
    funnel_stage: stage,
    traffic_source: source,
    product_id: productId,
  });
};

// Track social shares
export const trackSocialShare = (
  platform: string,
  contentType: string,
  contentId: string
) => {
  trackEvent('social_share', 'engagement', platform, undefined, {
    social_platform: platform,
    content_type: contentType,
    content_id: contentId,
  });
};

// Track email interactions
export const trackEmailInteraction = (
  action: 'open' | 'click' | 'unsubscribe',
  campaignId: string,
  emailType: string
) => {
  trackEvent('email_interaction', 'engagement', action, undefined, {
    email_action: action,
    campaign_id: campaignId,
    email_type: emailType,
  });
};

// Performance tracking
export const trackPerformanceMetric = (
  metricName: string,
  value: number,
  page: string
) => {
  trackEvent('performance_metric', 'technical', metricName, value, {
    metric_name: metricName,
    metric_value: value,
    page_path: page,
  });
};

// Error tracking
export const trackError = (
  errorType: string,
  errorMessage: string,
  page: string,
  severity: 'low' | 'medium' | 'high' = 'medium'
) => {
  trackEvent('error', 'technical', errorType, undefined, {
    error_type: errorType,
    error_message: errorMessage,
    error_severity: severity,
    page_path: page,
  });
};

// A/B testing tracking
export const trackABTest = (
  testName: string,
  variant: string,
  action: 'view' | 'convert'
) => {
  trackEvent('ab_test', 'optimization', testName, undefined, {
    test_name: testName,
    test_variant: variant,
    test_action: action,
  });
};

// Custom dimensions for enhanced tracking
export const setCustomDimensions = (dimensions: Record<string, string>) => {
  if (typeof window === 'undefined' || !window.gtag) return;
  
  window.gtag('config', GA_MEASUREMENT_ID, {
    custom_map: dimensions,
  });
};

// Initialize scroll depth tracking
export const initScrollTracking = () => {
  if (typeof window === 'undefined') return;
  
  let maxScroll = 0;
  const thresholds = [25, 50, 75, 90, 100];
  const tracked = new Set<number>();
  
  const handleScroll = () => {
    const scrollPercent = Math.round(
      (window.scrollY / (document.documentElement.scrollHeight - window.innerHeight)) * 100
    );
    
    if (scrollPercent > maxScroll) {
      maxScroll = scrollPercent;
      
      thresholds.forEach(threshold => {
        if (scrollPercent >= threshold && !tracked.has(threshold)) {
          tracked.add(threshold);
          trackScrollDepth(threshold, window.location.pathname);
        }
      });
    }
  };
  
  window.addEventListener('scroll', handleScroll, { passive: true });
  
  return () => {
    window.removeEventListener('scroll', handleScroll);
  };
};

// Initialize time tracking
export const initTimeTracking = () => {
  if (typeof window === 'undefined') return;
  
  const startTime = Date.now();
  
  const trackTime = () => {
    const timeSpent = Math.round((Date.now() - startTime) / 1000);
    trackTimeOnPage(timeSpent, window.location.pathname);
  };
  
  // Track time on page visibility change
  document.addEventListener('visibilitychange', () => {
    if (document.visibilityState === 'hidden') {
      trackTime();
    }
  });
  
  // Track time on page unload
  window.addEventListener('beforeunload', trackTime);
  
  return trackTime;
};
