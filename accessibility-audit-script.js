#!/usr/bin/env node

/**
 * OffGridFlux Accessibility Audit Script
 * Automated WCAG 2.1 AA compliance checking
 * 
 * Usage: node accessibility-audit-script.js
 * Requires: axe-core, puppeteer
 */

const puppeteer = require('puppeteer');
const { AxePuppeteer } = require('@axe-core/puppeteer');
const fs = require('fs');
const path = require('path');

const SITE_URL = 'http://localhost:3000';
const OUTPUT_DIR = './accessibility-reports';

// Pages to audit
const PAGES_TO_AUDIT = [
  { name: 'Homepage', url: '/' },
  { name: 'Category - Portable Power Stations', url: '/category/portable-power-stations' },
  { name: 'Product Page', url: '/product/jackery-explorer-1000' },
  { name: 'About Page', url: '/about' },
];

// WCAG 2.1 AA rules to enforce
const WCAG_AA_RULES = [
  'color-contrast',
  'keyboard',
  'aria-required-attr',
  'aria-valid-attr-value',
  'button-name',
  'image-alt',
  'label',
  'link-name',
  'list',
  'listitem',
];

async function auditPage(browser, pageConfig) {
  console.log(`🔍 Auditing: ${pageConfig.name}`);
  
  const page = await browser.newPage();
  await page.setViewport({ width: 1280, height: 720 });
  
  try {
    await page.goto(`${SITE_URL}${pageConfig.url}`, { 
      waitUntil: 'networkidle0',
      timeout: 30000 
    });

    // Run axe-core accessibility audit
    const results = await new AxePuppeteer(page)
      .withTags(['wcag2a', 'wcag2aa', 'wcag21aa'])
      .analyze();

    // Check color contrast manually for custom elements
    const contrastResults = await page.evaluate(() => {
      const elements = document.querySelectorAll('*');
      const contrastIssues = [];
      
      elements.forEach(el => {
        const style = window.getComputedStyle(el);
        const color = style.color;
        const backgroundColor = style.backgroundColor;
        
        // Skip elements with no text content
        if (!el.textContent.trim()) return;
        
        // Check for hardcoded colors (basic detection)
        if (color.includes('rgb(') && !color.includes('var(')) {
          contrastIssues.push({
            element: el.tagName.toLowerCase(),
            text: el.textContent.substring(0, 50),
            color: color,
            backgroundColor: backgroundColor,
            classes: el.className
          });
        }
      });
      
      return contrastIssues;
    });

    // Check for missing ARIA labels
    const ariaResults = await page.evaluate(() => {
      const interactiveElements = document.querySelectorAll('button, a, input, select, textarea');
      const ariaIssues = [];
      
      interactiveElements.forEach(el => {
        const hasAriaLabel = el.hasAttribute('aria-label');
        const hasAriaLabelledBy = el.hasAttribute('aria-labelledby');
        const hasVisibleText = el.textContent.trim().length > 0;
        const hasAltText = el.hasAttribute('alt');
        
        if (!hasAriaLabel && !hasAriaLabelledBy && !hasVisibleText && !hasAltText) {
          ariaIssues.push({
            element: el.tagName.toLowerCase(),
            classes: el.className,
            id: el.id,
            type: el.type || 'N/A'
          });
        }
      });
      
      return ariaIssues;
    });

    return {
      page: pageConfig.name,
      url: pageConfig.url,
      axeResults: results,
      contrastIssues: contrastResults,
      ariaIssues: ariaResults,
      timestamp: new Date().toISOString()
    };

  } catch (error) {
    console.error(`❌ Error auditing ${pageConfig.name}:`, error.message);
    return {
      page: pageConfig.name,
      url: pageConfig.url,
      error: error.message,
      timestamp: new Date().toISOString()
    };
  } finally {
    await page.close();
  }
}

async function generateReport(auditResults) {
  // Ensure output directory exists
  if (!fs.existsSync(OUTPUT_DIR)) {
    fs.mkdirSync(OUTPUT_DIR, { recursive: true });
  }

  // Generate detailed JSON report
  const jsonReport = {
    summary: {
      totalPages: auditResults.length,
      totalViolations: auditResults.reduce((sum, result) => 
        sum + (result.axeResults?.violations?.length || 0), 0),
      totalContrastIssues: auditResults.reduce((sum, result) => 
        sum + (result.contrastIssues?.length || 0), 0),
      totalAriaIssues: auditResults.reduce((sum, result) => 
        sum + (result.ariaIssues?.length || 0), 0),
      auditDate: new Date().toISOString()
    },
    results: auditResults
  };

  fs.writeFileSync(
    path.join(OUTPUT_DIR, 'accessibility-audit.json'),
    JSON.stringify(jsonReport, null, 2)
  );

  // Generate human-readable markdown report
  let markdownReport = `# OffGridFlux Accessibility Audit Report\n\n`;
  markdownReport += `**Generated:** ${new Date().toLocaleString()}\n\n`;
  markdownReport += `## Summary\n\n`;
  markdownReport += `- **Pages Audited:** ${jsonReport.summary.totalPages}\n`;
  markdownReport += `- **Total Violations:** ${jsonReport.summary.totalViolations}\n`;
  markdownReport += `- **Contrast Issues:** ${jsonReport.summary.totalContrastIssues}\n`;
  markdownReport += `- **ARIA Issues:** ${jsonReport.summary.totalAriaIssues}\n\n`;

  auditResults.forEach(result => {
    if (result.error) {
      markdownReport += `## ❌ ${result.page}\n\n**Error:** ${result.error}\n\n`;
      return;
    }

    markdownReport += `## ${result.page}\n\n`;
    markdownReport += `**URL:** ${result.url}\n\n`;

    if (result.axeResults?.violations?.length > 0) {
      markdownReport += `### Axe-Core Violations (${result.axeResults.violations.length})\n\n`;
      result.axeResults.violations.forEach(violation => {
        markdownReport += `- **${violation.id}** (${violation.impact}): ${violation.description}\n`;
        markdownReport += `  - Nodes affected: ${violation.nodes.length}\n`;
      });
      markdownReport += `\n`;
    }

    if (result.contrastIssues?.length > 0) {
      markdownReport += `### Color Contrast Issues (${result.contrastIssues.length})\n\n`;
      result.contrastIssues.slice(0, 5).forEach(issue => {
        markdownReport += `- **${issue.element}**: "${issue.text}" - ${issue.color} on ${issue.backgroundColor}\n`;
      });
      markdownReport += `\n`;
    }

    if (result.ariaIssues?.length > 0) {
      markdownReport += `### ARIA Issues (${result.ariaIssues.length})\n\n`;
      result.ariaIssues.slice(0, 5).forEach(issue => {
        markdownReport += `- **${issue.element}** (${issue.type}): Missing ARIA label\n`;
      });
      markdownReport += `\n`;
    }
  });

  fs.writeFileSync(
    path.join(OUTPUT_DIR, 'accessibility-audit.md'),
    markdownReport
  );

  console.log(`📊 Reports generated in ${OUTPUT_DIR}/`);
}

async function main() {
  console.log('🚀 Starting OffGridFlux Accessibility Audit...\n');

  const browser = await puppeteer.launch({ 
    headless: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });

  try {
    const auditResults = [];
    
    for (const pageConfig of PAGES_TO_AUDIT) {
      const result = await auditPage(browser, pageConfig);
      auditResults.push(result);
    }

    await generateReport(auditResults);
    
    console.log('\n✅ Accessibility audit completed!');
    console.log(`📁 Check ${OUTPUT_DIR}/ for detailed reports`);

  } catch (error) {
    console.error('❌ Audit failed:', error);
    process.exit(1);
  } finally {
    await browser.close();
  }
}

if (require.main === module) {
  main();
}

module.exports = { auditPage, generateReport };
