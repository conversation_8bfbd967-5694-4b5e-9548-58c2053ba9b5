// OffGridFlux - Footer Component
// Site footer with links, social proof, and legal information

import Link from 'next/link';
import { Zap, Mail, Shield, Award, ExternalLink } from 'lucide-react';

const footerLinks = {
  product: {
    title: 'Products',
    links: [
      { name: 'Portable Power Stations', href: '/category/portable-power-stations' },
      { name: 'Solar Generators', href: '/category/solar-generators' },
      { name: 'Home Battery Systems', href: '/category/home-battery-systems' },
      { name: 'Best of 2025', href: '/best-portable-power-stations-2025' }
    ]
  },
  content: {
    title: 'Content',
    links: [
      { name: 'Reviews', href: '/reviews' },
      { name: 'Buying Guides', href: '/guides' },
      { name: 'Comparisons', href: '/comparisons' },
      { name: 'Blog', href: '/blog' }
    ]
  },
  company: {
    title: 'Company',
    links: [
      { name: 'About Us', href: '/about' },
      { name: 'Testing Process', href: '/testing-process' },
      { name: 'Contact', href: '/contact' },
      { name: 'Newsletter', href: '#newsletter' }
    ]
  },
  legal: {
    title: 'Legal',
    links: [
      { name: 'Privacy Policy', href: '/privacy' },
      { name: 'Terms of Service', href: '/terms' },
      { name: 'Affiliate Disclosure', href: '/affiliate-disclosure' },
      { name: 'DMCA', href: '/dmca' }
    ]
  }
};

const brands = [
  { name: 'Jackery', href: '/brand/jackery' },
  { name: 'Goal Zero', href: '/brand/goal-zero' },
  { name: 'EcoFlow', href: '/brand/ecoflow' },
  { name: 'Bluetti', href: '/brand/bluetti' },
  { name: 'Anker', href: '/brand/anker' }
];

export function Footer() {
  return (
    <footer className="bg-neutral-900 text-white">
      {/* Main Footer Content */}
      <div className="max-w-7xl mx-auto px-4 py-16">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-8">
          
          {/* Brand Column */}
          <div className="lg:col-span-2">
            <div className="flex items-center space-x-2 mb-6">
              <div className="flex items-center justify-center w-8 h-8 bg-primary-600 rounded-lg">
                <Zap className="w-5 h-5 text-white" />
              </div>
              <span className="text-xl font-bold">OffGridFlux</span>
            </div>
            
            <p className="text-neutral-300 mb-6 leading-relaxed">
              Independent reviews and real-world testing of portable power stations. 
              Helping you find the perfect off-grid power solution since 2024.
            </p>
            
            {/* Trust Badges */}
            <div className="flex flex-wrap gap-3 mb-6">
              <div className="flex items-center gap-2 bg-neutral-800 rounded-lg px-3 py-2">
                <Shield className="w-4 h-4 text-green-400" />
                <span className="text-sm text-neutral-300">FTC Compliant</span>
              </div>
              <div className="flex items-center gap-2 bg-neutral-800 rounded-lg px-3 py-2">
                <Award className="w-4 h-4 text-yellow-400" />
                <span className="text-sm text-neutral-300">Expert Tested</span>
              </div>
            </div>

            {/* Newsletter CTA */}
            <div className="bg-neutral-800 rounded-lg p-4">
              <div className="flex items-center gap-2 mb-2">
                <Mail className="w-4 h-4 text-primary-400" />
                <span className="font-medium text-sm">Stay Updated</span>
              </div>
              <p className="text-sm text-neutral-400 mb-3">
                Get the latest reviews and deals in your inbox.
              </p>
              <Link 
                href="#newsletter"
                className="inline-flex items-center text-sm text-primary-400 hover:text-primary-300 font-medium"
              >
                Subscribe Now
                <ExternalLink className="w-3 h-3 ml-1" />
              </Link>
            </div>
          </div>

          {/* Links Columns */}
          {Object.entries(footerLinks).map(([key, section]) => (
            <div key={key}>
              <h3 className="font-semibold text-white mb-4">{section.title}</h3>
              <ul className="space-y-3">
                {section.links.map((link) => (
                  <li key={link.name}>
                    <Link 
                      href={link.href}
                      className="text-neutral-300 hover:text-white transition-colors text-sm"
                    >
                      {link.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>

        {/* Brands Section */}
        <div className="border-t border-neutral-800 mt-12 pt-8">
          <h3 className="font-semibold text-white mb-4">Brands We Review</h3>
          <div className="flex flex-wrap gap-4">
            {brands.map((brand) => (
              <Link
                key={brand.name}
                href={brand.href}
                className="text-neutral-400 hover:text-white transition-colors text-sm bg-neutral-800 hover:bg-neutral-700 px-3 py-2 rounded-lg"
              >
                {brand.name}
              </Link>
            ))}
          </div>
        </div>
      </div>

      {/* Bottom Bar */}
      <div className="border-t border-neutral-800">
        <div className="max-w-7xl mx-auto px-4 py-6">
          <div className="flex flex-col md:flex-row justify-between items-center gap-4">
            <div className="text-sm text-neutral-400">
              © 2025 OffGridFlux. All rights reserved.
            </div>
            
            <div className="flex items-center gap-6 text-sm text-neutral-400">
              <span>Made with ⚡ for the off-grid community</span>
              <div className="flex items-center gap-1">
                <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                <span>17+ Products Tested</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
