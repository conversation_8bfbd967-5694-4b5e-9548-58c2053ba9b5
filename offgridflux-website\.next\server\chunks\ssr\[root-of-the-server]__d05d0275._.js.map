{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_9e72d27f.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"inter_9e72d27f-module__JKMi0a__className\",\n  \"variable\": \"inter_9e72d27f-module__JKMi0a__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 16, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_9e72d27f.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Inter%22,%22arguments%22:[{%22subsets%22:[%22latin%22],%22variable%22:%22--font-inter%22}],%22variableName%22:%22inter%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Inter', 'Inter Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,qJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,qJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,qJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 38, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/offgridflux/offgridflux-website/src/components/GoogleAnalytics.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const GoogleAnalytics = registerClientReference(\n    function() { throw new Error(\"Attempted to call GoogleAnalytics() from the server but GoogleAnalytics is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/GoogleAnalytics.tsx <module evaluation>\",\n    \"GoogleAnalytics\",\n);\nexport const useAffiliateTracking = registerClientReference(\n    function() { throw new Error(\"Attempted to call useAffiliateTracking() from the server but useAffiliateTracking is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/GoogleAnalytics.tsx <module evaluation>\",\n    \"useAffiliateTracking\",\n);\nexport const useContentTracking = registerClientReference(\n    function() { throw new Error(\"Attempted to call useContentTracking() from the server but useContentTracking is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/GoogleAnalytics.tsx <module evaluation>\",\n    \"useContentTracking\",\n);\nexport const useNewsletterTracking = registerClientReference(\n    function() { throw new Error(\"Attempted to call useNewsletterTracking() from the server but useNewsletterTracking is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/GoogleAnalytics.tsx <module evaluation>\",\n    \"useNewsletterTracking\",\n);\nexport const useProductTracking = registerClientReference(\n    function() { throw new Error(\"Attempted to call useProductTracking() from the server but useProductTracking is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/GoogleAnalytics.tsx <module evaluation>\",\n    \"useProductTracking\",\n);\nexport const useSearchTracking = registerClientReference(\n    function() { throw new Error(\"Attempted to call useSearchTracking() from the server but useSearchTracking is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/GoogleAnalytics.tsx <module evaluation>\",\n    \"useSearchTracking\",\n);\n"], "names": [], "mappings": ";;;;;;;;AAAA;;AACO,MAAM,kBAAkB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,oEACA;AAEG,MAAM,uBAAuB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACtD;IAAa,MAAM,IAAI,MAAM;AAAwP,GACrR,oEACA;AAEG,MAAM,qBAAqB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,oEACA;AAEG,MAAM,wBAAwB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACvD;IAAa,MAAM,IAAI,MAAM;AAA0P,GACvR,oEACA;AAEG,MAAM,qBAAqB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,oEACA;AAEG,MAAM,oBAAoB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,oEACA", "debugId": null}}, {"offset": {"line": 72, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/offgridflux/offgridflux-website/src/components/GoogleAnalytics.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const GoogleAnalytics = registerClientReference(\n    function() { throw new Error(\"Attempted to call GoogleAnalytics() from the server but GoogleAnalytics is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/GoogleAnalytics.tsx\",\n    \"GoogleAnalytics\",\n);\nexport const useAffiliateTracking = registerClientReference(\n    function() { throw new Error(\"Attempted to call useAffiliateTracking() from the server but useAffiliateTracking is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/GoogleAnalytics.tsx\",\n    \"useAffiliateTracking\",\n);\nexport const useContentTracking = registerClientReference(\n    function() { throw new Error(\"Attempted to call useContentTracking() from the server but useContentTracking is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/GoogleAnalytics.tsx\",\n    \"useContentTracking\",\n);\nexport const useNewsletterTracking = registerClientReference(\n    function() { throw new Error(\"Attempted to call useNewsletterTracking() from the server but useNewsletterTracking is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/GoogleAnalytics.tsx\",\n    \"useNewsletterTracking\",\n);\nexport const useProductTracking = registerClientReference(\n    function() { throw new Error(\"Attempted to call useProductTracking() from the server but useProductTracking is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/GoogleAnalytics.tsx\",\n    \"useProductTracking\",\n);\nexport const useSearchTracking = registerClientReference(\n    function() { throw new Error(\"Attempted to call useSearchTracking() from the server but useSearchTracking is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/GoogleAnalytics.tsx\",\n    \"useSearchTracking\",\n);\n"], "names": [], "mappings": ";;;;;;;;AAAA;;AACO,MAAM,kBAAkB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,gDACA;AAEG,MAAM,uBAAuB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACtD;IAAa,MAAM,IAAI,MAAM;AAAwP,GACrR,gDACA;AAEG,MAAM,qBAAqB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,gDACA;AAEG,MAAM,wBAAwB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACvD;IAAa,MAAM,IAAI,MAAM;AAA0P,GACvR,gDACA;AAEG,MAAM,qBAAqB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,gDACA;AAEG,MAAM,oBAAoB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,gDACA", "debugId": null}}, {"offset": {"line": 106, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 116, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/offgridflux/offgridflux-website/src/app/layout.tsx"], "sourcesContent": ["import type { Metadata } from \"next\";\nimport { Inter } from \"next/font/google\";\nimport { GoogleAnalytics } from \"@/components/GoogleAnalytics\";\nimport \"./globals.css\";\n\nconst inter = Inter({\n  subsets: [\"latin\"],\n  variable: \"--font-inter\",\n});\n\nexport const metadata: Metadata = {\n  title: {\n    default: \"OffGridFlux - Best Portable Power Stations 2025 | Expert Reviews\",\n    template: \"%s | OffGridFlux\"\n  },\n  description: \"Expert reviews and real-world testing of portable power stations from Jackery, Goal Zero, and EcoFlow. Find your perfect off-grid power solution.\",\n  keywords: \"portable power station, solar generator, off grid power, jackery, goal zero, ecoflow, battery backup, camping power\",\n  authors: [{ name: \"OffGridFlux Team\" }],\n  creator: \"OffGridFlux\",\n  publisher: \"OffGridFlux\",\n  metadataBase: new URL(\"https://offgridflux.com\"),\n  openGraph: {\n    type: \"website\",\n    locale: \"en_US\",\n    url: \"https://offgridflux.com\",\n    siteName: \"OffGridFlux\",\n    title: \"OffGridFlux - Best Portable Power Stations 2025\",\n    description: \"Expert reviews and real-world testing of portable power stations. Find your perfect off-grid power solution.\",\n    images: [\n      {\n        url: \"/og-image.jpg\",\n        width: 1200,\n        height: 630,\n        alt: \"OffGridFlux - Portable Power Station Reviews\"\n      }\n    ]\n  },\n  twitter: {\n    card: \"summary_large_image\",\n    title: \"OffGridFlux - Best Portable Power Stations 2025\",\n    description: \"Expert reviews and real-world testing of portable power stations.\",\n    images: [\"/og-image.jpg\"]\n  },\n  robots: {\n    index: true,\n    follow: true,\n    googleBot: {\n      index: true,\n      follow: true,\n      \"max-video-preview\": -1,\n      \"max-image-preview\": \"large\",\n      \"max-snippet\": -1\n    }\n  }\n};\n\nexport default function RootLayout({\n  children,\n}: Readonly<{\n  children: React.ReactNode;\n}>) {\n  return (\n    <html lang=\"en\">\n      <head>\n        <GoogleAnalytics />\n      </head>\n      <body className={`${inter.variable} font-sans antialiased`}>\n        {children}\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;;;AAEA;;;;;AAQO,MAAM,WAAqB;IAChC,OAAO;QACL,SAAS;QACT,UAAU;IACZ;IACA,aAAa;IACb,UAAU;IACV,SAAS;QAAC;YAAE,MAAM;QAAmB;KAAE;IACvC,SAAS;IACT,WAAW;IACX,cAAc,IAAI,IAAI;IACtB,WAAW;QACT,MAAM;QACN,QAAQ;QACR,KAAK;QACL,UAAU;QACV,OAAO;QACP,aAAa;QACb,QAAQ;YACN;gBACE,KAAK;gBACL,OAAO;gBACP,QAAQ;gBACR,KAAK;YACP;SACD;IACH;IACA,SAAS;QACP,MAAM;QACN,OAAO;QACP,aAAa;QACb,QAAQ;YAAC;SAAgB;IAC3B;IACA,QAAQ;QACN,OAAO;QACP,QAAQ;QACR,WAAW;YACT,OAAO;YACP,QAAQ;YACR,qBAAqB,CAAC;YACtB,qBAAqB;YACrB,eAAe,CAAC;QAClB;IACF;AACF;AAEe,SAAS,WAAW,EACjC,QAAQ,EAGR;IACA,qBACE,8OAAC;QAAK,MAAK;;0BACT,8OAAC;0BACC,cAAA,8OAAC,qIAAA,CAAA,kBAAe;;;;;;;;;;0BAElB,8OAAC;gBAAK,WAAW,GAAG,yIAAA,CAAA,UAAK,CAAC,QAAQ,CAAC,sBAAsB,CAAC;0BACvD;;;;;;;;;;;;AAIT", "debugId": null}}, {"offset": {"line": 213, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/offgridflux/offgridflux-website/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-rsc'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}]}