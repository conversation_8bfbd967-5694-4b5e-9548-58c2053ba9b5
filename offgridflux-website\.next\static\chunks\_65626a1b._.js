(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/lib/analytics.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// OffGridFlux - Analytics Integration
// Google Analytics 4 and conversion tracking
__turbopack_context__.s({
    "GA_MEASUREMENT_ID": (()=>GA_MEASUREMENT_ID),
    "initGA": (()=>initGA),
    "initScrollTracking": (()=>initScrollTracking),
    "initTimeTracking": (()=>initTimeTracking),
    "setCustomDimensions": (()=>setCustomDimensions),
    "trackABTest": (()=>trackABTest),
    "trackAffiliateClick": (()=>trackAffiliateClick),
    "trackContentEngagement": (()=>trackContentEngagement),
    "trackConversionFunnel": (()=>trackConversionFunnel),
    "trackEmailInteraction": (()=>trackEmailInteraction),
    "trackError": (()=>trackError),
    "trackEvent": (()=>trackEvent),
    "trackNewsletterSignup": (()=>trackNewsletterSignup),
    "trackPageView": (()=>trackPageView),
    "trackPerformanceMetric": (()=>trackPerformanceMetric),
    "trackProductView": (()=>trackProductView),
    "trackPurchaseIntent": (()=>trackPurchaseIntent),
    "trackScrollDepth": (()=>trackScrollDepth),
    "trackSearch": (()=>trackSearch),
    "trackSocialShare": (()=>trackSocialShare),
    "trackTimeOnPage": (()=>trackTimeOnPage),
    "trackUserPreference": (()=>trackUserPreference)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
const GA_MEASUREMENT_ID = ("TURBOPACK compile-time value", "") || 'G-XXXXXXXXXX';
const initGA = ()=>{
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
    // Load Google Analytics script
    const script = document.createElement('script');
    script.src = `https://www.googletagmanager.com/gtag/js?id=${GA_MEASUREMENT_ID}`;
    script.async = true;
    document.head.appendChild(script);
    // Initialize dataLayer and gtag
    window.dataLayer = window.dataLayer || [];
    window.gtag = function gtag(...args) {
        window.dataLayer.push(args);
    };
    window.gtag('js', new Date());
    window.gtag('config', GA_MEASUREMENT_ID, {
        page_title: document.title,
        page_location: window.location.href
    });
};
const trackPageView = (url, title)=>{
    if ("object" === 'undefined' || !window.gtag) return;
    window.gtag('config', GA_MEASUREMENT_ID, {
        page_title: title || document.title,
        page_location: url
    });
};
const trackEvent = (action, category, label, value, customParameters)=>{
    if ("object" === 'undefined' || !window.gtag) return;
    window.gtag('event', action, {
        event_category: category,
        event_label: label,
        value: value,
        ...customParameters
    });
};
const trackAffiliateClick = (productName, productId, brand, price, url)=>{
    trackEvent('affiliate_click', 'ecommerce', productName, price, {
        product_id: productId,
        product_name: productName,
        product_brand: brand,
        product_price: price,
        affiliate_url: url,
        currency: 'USD'
    });
};
const trackProductView = (productName, productId, brand, price, category)=>{
    if ("object" === 'undefined' || !window.gtag) return;
    window.gtag('event', 'view_item', {
        currency: 'USD',
        value: price,
        items: [
            {
                item_id: productId,
                item_name: productName,
                item_brand: brand,
                item_category: category,
                price: price,
                quantity: 1
            }
        ]
    });
};
const trackNewsletterSignup = (source)=>{
    trackEvent('newsletter_signup', 'engagement', source, undefined, {
        signup_source: source
    });
};
const trackSearch = (searchTerm, resultsCount)=>{
    trackEvent('search', 'engagement', searchTerm, resultsCount, {
        search_term: searchTerm,
        results_count: resultsCount
    });
};
const trackContentEngagement = (contentType, contentId, engagementType, value)=>{
    trackEvent(engagementType, 'content', contentId, value, {
        content_type: contentType,
        content_id: contentId
    });
};
const trackScrollDepth = (percentage, page)=>{
    trackEvent('scroll_depth', 'engagement', page, percentage, {
        scroll_percentage: percentage,
        page_path: page
    });
};
const trackTimeOnPage = (timeInSeconds, page)=>{
    trackEvent('time_on_page', 'engagement', page, timeInSeconds, {
        time_seconds: timeInSeconds,
        page_path: page
    });
};
const trackPurchaseIntent = (productName, productId, brand, price, step)=>{
    const eventName = step === 'view_review' ? 'view_item' : step === 'click_affiliate' ? 'select_item' : 'add_to_cart';
    if ("object" === 'undefined' || !window.gtag) return;
    window.gtag('event', eventName, {
        currency: 'USD',
        value: price,
        items: [
            {
                item_id: productId,
                item_name: productName,
                item_brand: brand,
                item_category: 'Portable Power Station',
                price: price,
                quantity: 1
            }
        ]
    });
};
const trackUserPreference = (preferenceType, preferenceValue)=>{
    trackEvent('user_preference', 'personalization', preferenceType, undefined, {
        preference_type: preferenceType,
        preference_value: preferenceValue
    });
};
const trackConversionFunnel = (stage, source, productId)=>{
    trackEvent('conversion_funnel', 'ecommerce', stage, undefined, {
        funnel_stage: stage,
        traffic_source: source,
        product_id: productId
    });
};
const trackSocialShare = (platform, contentType, contentId)=>{
    trackEvent('social_share', 'engagement', platform, undefined, {
        social_platform: platform,
        content_type: contentType,
        content_id: contentId
    });
};
const trackEmailInteraction = (action, campaignId, emailType)=>{
    trackEvent('email_interaction', 'engagement', action, undefined, {
        email_action: action,
        campaign_id: campaignId,
        email_type: emailType
    });
};
const trackPerformanceMetric = (metricName, value, page)=>{
    trackEvent('performance_metric', 'technical', metricName, value, {
        metric_name: metricName,
        metric_value: value,
        page_path: page
    });
};
const trackError = (errorType, errorMessage, page, severity = 'medium')=>{
    trackEvent('error', 'technical', errorType, undefined, {
        error_type: errorType,
        error_message: errorMessage,
        error_severity: severity,
        page_path: page
    });
};
const trackABTest = (testName, variant, action)=>{
    trackEvent('ab_test', 'optimization', testName, undefined, {
        test_name: testName,
        test_variant: variant,
        test_action: action
    });
};
const setCustomDimensions = (dimensions)=>{
    if ("object" === 'undefined' || !window.gtag) return;
    window.gtag('config', GA_MEASUREMENT_ID, {
        custom_map: dimensions
    });
};
const initScrollTracking = ()=>{
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
    let maxScroll = 0;
    const thresholds = [
        25,
        50,
        75,
        90,
        100
    ];
    const tracked = new Set();
    const handleScroll = ()=>{
        const scrollPercent = Math.round(window.scrollY / (document.documentElement.scrollHeight - window.innerHeight) * 100);
        if (scrollPercent > maxScroll) {
            maxScroll = scrollPercent;
            thresholds.forEach((threshold)=>{
                if (scrollPercent >= threshold && !tracked.has(threshold)) {
                    tracked.add(threshold);
                    trackScrollDepth(threshold, window.location.pathname);
                }
            });
        }
    };
    window.addEventListener('scroll', handleScroll, {
        passive: true
    });
    return ()=>{
        window.removeEventListener('scroll', handleScroll);
    };
};
const initTimeTracking = ()=>{
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
    const startTime = Date.now();
    const trackTime = ()=>{
        const timeSpent = Math.round((Date.now() - startTime) / 1000);
        trackTimeOnPage(timeSpent, window.location.pathname);
    };
    // Track time on page visibility change
    document.addEventListener('visibilitychange', ()=>{
        if (document.visibilityState === 'hidden') {
            trackTime();
        }
    });
    // Track time on page unload
    window.addEventListener('beforeunload', trackTime);
    return trackTime;
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/GoogleAnalytics.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// OffGridFlux - Google Analytics Component
// Client-side analytics initialization and tracking
__turbopack_context__.s({
    "GoogleAnalytics": (()=>GoogleAnalytics),
    "useAffiliateTracking": (()=>useAffiliateTracking),
    "useContentTracking": (()=>useContentTracking),
    "useNewsletterTracking": (()=>useNewsletterTracking),
    "useProductTracking": (()=>useProductTracking),
    "useSearchTracking": (()=>useSearchTracking)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$analytics$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/analytics.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
function GoogleAnalytics() {
    _s();
    const pathname = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usePathname"])();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "GoogleAnalytics.useEffect": ()=>{
            // Initialize Google Analytics
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$analytics$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["initGA"])();
            // Initialize scroll and time tracking
            const cleanupScroll = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$analytics$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["initScrollTracking"])();
            const cleanupTime = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$analytics$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["initTimeTracking"])();
            return ({
                "GoogleAnalytics.useEffect": ()=>{
                    if (cleanupScroll) cleanupScroll();
                    if (cleanupTime) cleanupTime();
                }
            })["GoogleAnalytics.useEffect"];
        }
    }["GoogleAnalytics.useEffect"], []);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "GoogleAnalytics.useEffect": ()=>{
            // Track page views on route changes
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$analytics$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["trackPageView"])(pathname);
        }
    }["GoogleAnalytics.useEffect"], [
        pathname
    ]);
    // Only render in production with valid measurement ID
    if ("TURBOPACK compile-time truthy", 1) {
        return null;
    }
    "TURBOPACK unreachable";
}
_s(GoogleAnalytics, "tjXKfJWuFDa0epp0CJaCeazyqhM=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usePathname"]
    ];
});
_c = GoogleAnalytics;
function useAffiliateTracking() {
    const trackClick = (productName, productId, brand, price, url)=>{
        // Import analytics functions dynamically to avoid SSR issues
        __turbopack_context__.r("[project]/src/lib/analytics.ts [app-client] (ecmascript, async loader)")(__turbopack_context__.i).then(({ trackAffiliateClick })=>{
            trackAffiliateClick(productName, productId, brand, price, url);
        });
    };
    return {
        trackClick
    };
}
function useProductTracking() {
    const trackView = (productName, productId, brand, price, category)=>{
        __turbopack_context__.r("[project]/src/lib/analytics.ts [app-client] (ecmascript, async loader)")(__turbopack_context__.i).then(({ trackProductView })=>{
            trackProductView(productName, productId, brand, price, category);
        });
    };
    return {
        trackView
    };
}
function useNewsletterTracking() {
    const trackSignup = (source)=>{
        __turbopack_context__.r("[project]/src/lib/analytics.ts [app-client] (ecmascript, async loader)")(__turbopack_context__.i).then(({ trackNewsletterSignup })=>{
            trackNewsletterSignup(source);
        });
    };
    return {
        trackSignup
    };
}
function useSearchTracking() {
    const trackSearch = (searchTerm, resultsCount)=>{
        __turbopack_context__.r("[project]/src/lib/analytics.ts [app-client] (ecmascript, async loader)")(__turbopack_context__.i).then(({ trackSearch })=>{
            trackSearch(searchTerm, resultsCount);
        });
    };
    return {
        trackSearch
    };
}
function useContentTracking() {
    const trackEngagement = (contentType, contentId, engagementType, value)=>{
        __turbopack_context__.r("[project]/src/lib/analytics.ts [app-client] (ecmascript, async loader)")(__turbopack_context__.i).then(({ trackContentEngagement })=>{
            trackContentEngagement(contentType, contentId, engagementType, value);
        });
    };
    return {
        trackEngagement
    };
}
var _c;
__turbopack_context__.k.register(_c, "GoogleAnalytics");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
/**
 * @license React
 * react-jsx-dev-runtime.development.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */ var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
"use strict";
"production" !== ("TURBOPACK compile-time value", "development") && function() {
    function getComponentNameFromType(type) {
        if (null == type) return null;
        if ("function" === typeof type) return type.$$typeof === REACT_CLIENT_REFERENCE ? null : type.displayName || type.name || null;
        if ("string" === typeof type) return type;
        switch(type){
            case REACT_FRAGMENT_TYPE:
                return "Fragment";
            case REACT_PROFILER_TYPE:
                return "Profiler";
            case REACT_STRICT_MODE_TYPE:
                return "StrictMode";
            case REACT_SUSPENSE_TYPE:
                return "Suspense";
            case REACT_SUSPENSE_LIST_TYPE:
                return "SuspenseList";
            case REACT_ACTIVITY_TYPE:
                return "Activity";
        }
        if ("object" === typeof type) switch("number" === typeof type.tag && console.error("Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue."), type.$$typeof){
            case REACT_PORTAL_TYPE:
                return "Portal";
            case REACT_CONTEXT_TYPE:
                return (type.displayName || "Context") + ".Provider";
            case REACT_CONSUMER_TYPE:
                return (type._context.displayName || "Context") + ".Consumer";
            case REACT_FORWARD_REF_TYPE:
                var innerType = type.render;
                type = type.displayName;
                type || (type = innerType.displayName || innerType.name || "", type = "" !== type ? "ForwardRef(" + type + ")" : "ForwardRef");
                return type;
            case REACT_MEMO_TYPE:
                return innerType = type.displayName || null, null !== innerType ? innerType : getComponentNameFromType(type.type) || "Memo";
            case REACT_LAZY_TYPE:
                innerType = type._payload;
                type = type._init;
                try {
                    return getComponentNameFromType(type(innerType));
                } catch (x) {}
        }
        return null;
    }
    function testStringCoercion(value) {
        return "" + value;
    }
    function checkKeyStringCoercion(value) {
        try {
            testStringCoercion(value);
            var JSCompiler_inline_result = !1;
        } catch (e) {
            JSCompiler_inline_result = !0;
        }
        if (JSCompiler_inline_result) {
            JSCompiler_inline_result = console;
            var JSCompiler_temp_const = JSCompiler_inline_result.error;
            var JSCompiler_inline_result$jscomp$0 = "function" === typeof Symbol && Symbol.toStringTag && value[Symbol.toStringTag] || value.constructor.name || "Object";
            JSCompiler_temp_const.call(JSCompiler_inline_result, "The provided key is an unsupported type %s. This value must be coerced to a string before using it here.", JSCompiler_inline_result$jscomp$0);
            return testStringCoercion(value);
        }
    }
    function getTaskName(type) {
        if (type === REACT_FRAGMENT_TYPE) return "<>";
        if ("object" === typeof type && null !== type && type.$$typeof === REACT_LAZY_TYPE) return "<...>";
        try {
            var name = getComponentNameFromType(type);
            return name ? "<" + name + ">" : "<...>";
        } catch (x) {
            return "<...>";
        }
    }
    function getOwner() {
        var dispatcher = ReactSharedInternals.A;
        return null === dispatcher ? null : dispatcher.getOwner();
    }
    function UnknownOwner() {
        return Error("react-stack-top-frame");
    }
    function hasValidKey(config) {
        if (hasOwnProperty.call(config, "key")) {
            var getter = Object.getOwnPropertyDescriptor(config, "key").get;
            if (getter && getter.isReactWarning) return !1;
        }
        return void 0 !== config.key;
    }
    function defineKeyPropWarningGetter(props, displayName) {
        function warnAboutAccessingKey() {
            specialPropKeyWarningShown || (specialPropKeyWarningShown = !0, console.error("%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)", displayName));
        }
        warnAboutAccessingKey.isReactWarning = !0;
        Object.defineProperty(props, "key", {
            get: warnAboutAccessingKey,
            configurable: !0
        });
    }
    function elementRefGetterWithDeprecationWarning() {
        var componentName = getComponentNameFromType(this.type);
        didWarnAboutElementRef[componentName] || (didWarnAboutElementRef[componentName] = !0, console.error("Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release."));
        componentName = this.props.ref;
        return void 0 !== componentName ? componentName : null;
    }
    function ReactElement(type, key, self, source, owner, props, debugStack, debugTask) {
        self = props.ref;
        type = {
            $$typeof: REACT_ELEMENT_TYPE,
            type: type,
            key: key,
            props: props,
            _owner: owner
        };
        null !== (void 0 !== self ? self : null) ? Object.defineProperty(type, "ref", {
            enumerable: !1,
            get: elementRefGetterWithDeprecationWarning
        }) : Object.defineProperty(type, "ref", {
            enumerable: !1,
            value: null
        });
        type._store = {};
        Object.defineProperty(type._store, "validated", {
            configurable: !1,
            enumerable: !1,
            writable: !0,
            value: 0
        });
        Object.defineProperty(type, "_debugInfo", {
            configurable: !1,
            enumerable: !1,
            writable: !0,
            value: null
        });
        Object.defineProperty(type, "_debugStack", {
            configurable: !1,
            enumerable: !1,
            writable: !0,
            value: debugStack
        });
        Object.defineProperty(type, "_debugTask", {
            configurable: !1,
            enumerable: !1,
            writable: !0,
            value: debugTask
        });
        Object.freeze && (Object.freeze(type.props), Object.freeze(type));
        return type;
    }
    function jsxDEVImpl(type, config, maybeKey, isStaticChildren, source, self, debugStack, debugTask) {
        var children = config.children;
        if (void 0 !== children) if (isStaticChildren) if (isArrayImpl(children)) {
            for(isStaticChildren = 0; isStaticChildren < children.length; isStaticChildren++)validateChildKeys(children[isStaticChildren]);
            Object.freeze && Object.freeze(children);
        } else console.error("React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.");
        else validateChildKeys(children);
        if (hasOwnProperty.call(config, "key")) {
            children = getComponentNameFromType(type);
            var keys = Object.keys(config).filter(function(k) {
                return "key" !== k;
            });
            isStaticChildren = 0 < keys.length ? "{key: someKey, " + keys.join(": ..., ") + ": ...}" : "{key: someKey}";
            didWarnAboutKeySpread[children + isStaticChildren] || (keys = 0 < keys.length ? "{" + keys.join(": ..., ") + ": ...}" : "{}", console.error('A props object containing a "key" prop is being spread into JSX:\n  let props = %s;\n  <%s {...props} />\nReact keys must be passed directly to JSX without using spread:\n  let props = %s;\n  <%s key={someKey} {...props} />', isStaticChildren, children, keys, children), didWarnAboutKeySpread[children + isStaticChildren] = !0);
        }
        children = null;
        void 0 !== maybeKey && (checkKeyStringCoercion(maybeKey), children = "" + maybeKey);
        hasValidKey(config) && (checkKeyStringCoercion(config.key), children = "" + config.key);
        if ("key" in config) {
            maybeKey = {};
            for(var propName in config)"key" !== propName && (maybeKey[propName] = config[propName]);
        } else maybeKey = config;
        children && defineKeyPropWarningGetter(maybeKey, "function" === typeof type ? type.displayName || type.name || "Unknown" : type);
        return ReactElement(type, children, self, source, getOwner(), maybeKey, debugStack, debugTask);
    }
    function validateChildKeys(node) {
        "object" === typeof node && null !== node && node.$$typeof === REACT_ELEMENT_TYPE && node._store && (node._store.validated = 1);
    }
    var React = __turbopack_context__.r("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)"), REACT_ELEMENT_TYPE = Symbol.for("react.transitional.element"), REACT_PORTAL_TYPE = Symbol.for("react.portal"), REACT_FRAGMENT_TYPE = Symbol.for("react.fragment"), REACT_STRICT_MODE_TYPE = Symbol.for("react.strict_mode"), REACT_PROFILER_TYPE = Symbol.for("react.profiler");
    Symbol.for("react.provider");
    var REACT_CONSUMER_TYPE = Symbol.for("react.consumer"), REACT_CONTEXT_TYPE = Symbol.for("react.context"), REACT_FORWARD_REF_TYPE = Symbol.for("react.forward_ref"), REACT_SUSPENSE_TYPE = Symbol.for("react.suspense"), REACT_SUSPENSE_LIST_TYPE = Symbol.for("react.suspense_list"), REACT_MEMO_TYPE = Symbol.for("react.memo"), REACT_LAZY_TYPE = Symbol.for("react.lazy"), REACT_ACTIVITY_TYPE = Symbol.for("react.activity"), REACT_CLIENT_REFERENCE = Symbol.for("react.client.reference"), ReactSharedInternals = React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE, hasOwnProperty = Object.prototype.hasOwnProperty, isArrayImpl = Array.isArray, createTask = console.createTask ? console.createTask : function() {
        return null;
    };
    React = {
        "react-stack-bottom-frame": function(callStackForError) {
            return callStackForError();
        }
    };
    var specialPropKeyWarningShown;
    var didWarnAboutElementRef = {};
    var unknownOwnerDebugStack = React["react-stack-bottom-frame"].bind(React, UnknownOwner)();
    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));
    var didWarnAboutKeySpread = {};
    exports.Fragment = REACT_FRAGMENT_TYPE;
    exports.jsxDEV = function(type, config, maybeKey, isStaticChildren, source, self) {
        var trackActualOwner = 1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;
        return jsxDEVImpl(type, config, maybeKey, isStaticChildren, source, self, trackActualOwner ? Error("react-stack-top-frame") : unknownOwnerDebugStack, trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask);
    };
}();
}}),
"[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
'use strict';
if ("TURBOPACK compile-time falsy", 0) {
    "TURBOPACK unreachable";
} else {
    module.exports = __turbopack_context__.r("[project]/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js [app-client] (ecmascript)");
}
}}),
"[project]/node_modules/next/navigation.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
module.exports = __turbopack_context__.r("[project]/node_modules/next/dist/client/components/navigation.js [app-client] (ecmascript)");
}}),
}]);

//# sourceMappingURL=_65626a1b._.js.map