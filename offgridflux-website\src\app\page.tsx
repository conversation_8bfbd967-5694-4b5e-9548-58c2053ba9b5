// OffGridFlux - Homepage
// Main landing page showcasing featured products and categories

import { Metadata } from 'next';
import Link from 'next/link';
import { getFeaturedProducts, getBestValueProducts } from '@/lib/supabase';
import { ProductCard } from '@/components/ProductCard';
import { Hero } from '@/components/Hero';
import { CategoryGrid } from '@/components/CategoryGrid';
import { NewsletterSignup } from '@/components/NewsletterSignup';
import { TrustSignals } from '@/components/TrustSignals';

export const metadata: Metadata = {
  title: 'OffGridFlux - Best Portable Power Stations 2025 | Expert Reviews & Testing',
  description: 'Unbiased reviews and real-world testing of portable power stations from Jackery, Goal Zero, and EcoFlow. Find the perfect off-grid power solution for camping, emergencies, and more.',
  keywords: 'portable power station, solar generator, off grid power, jackery, goal zero, ecoflow, battery backup, camping power',
  openGraph: {
    title: 'OffGridFlux - Best Portable Power Stations 2025',
    description: 'Expert reviews and real-world testing of portable power stations. Find your perfect off-grid power solution.',
    type: 'website',
    url: 'https://offgridflux.com',
  }
};

export default async function HomePage() {
  // Fetch featured and best value products
  const [featuredResult, bestValueResult] = await Promise.all([
    getFeaturedProducts(6),
    getBestValueProducts(5)
  ]);

  const featuredProducts = featuredResult.data || [];
  const bestValueProducts = bestValueResult.data || [];

  return (
    <div className="bg-white">
      {/* Hero Section */}
      <Hero />

      {/* Trust Signals */}
      <TrustSignals />

      {/* Featured Products */}
      <section className="py-20 bg-neutral-50">
        <div className="max-w-7xl mx-auto px-4">
          <div className="text-center mb-16">
            <div className="inline-flex items-center gap-2 bg-primary-100 text-primary-700 rounded-full px-4 py-2 text-sm font-medium mb-4">
              <span className="w-2 h-2 bg-primary-500 rounded-full"></span>
              Editor&apos;s Choice
            </div>
            <h2 className="text-4xl font-bold text-neutral-900 mb-6">
              Top Rated Power Stations
            </h2>
            <p className="text-xl text-neutral-600 max-w-3xl mx-auto leading-relaxed">
              Our highest-rated portable power stations based on rigorous real-world testing,
              user feedback, and expert analysis.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {featuredProducts.map((product) => (
              <ProductCard key={product.id} product={product} />
            ))}
          </div>

          <div className="text-center mt-12">
            <Link
              href="/category/portable-power-stations"
              className="inline-flex items-center gap-2 bg-primary-600 hover:bg-primary-700 text-white font-semibold py-4 px-8 rounded-xl transition-all duration-200 hover:scale-105 shadow-lg hover:shadow-xl"
            >
              View All Power Stations
              <span className="text-primary-200">→</span>
            </Link>
          </div>
        </div>
      </section>

      {/* Best Value Section */}
      {bestValueProducts.length > 0 && (
        <section className="py-20 bg-white border-t border-neutral-200">
          <div className="max-w-7xl mx-auto px-4">
            <div className="text-center mb-16">
              <div className="inline-flex items-center gap-2 bg-energy-100 text-energy-700 rounded-full px-4 py-2 text-sm font-medium mb-4">
                <span className="w-2 h-2 bg-energy-500 rounded-full"></span>
                Best Value
              </div>
              <h2 className="text-4xl font-bold text-neutral-900 mb-6">
                Maximum Capacity Per Dollar
              </h2>
              <p className="text-xl text-neutral-600 max-w-3xl mx-auto leading-relaxed">
                Get the most power capacity for your investment with these exceptional value picks.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-6">
              {bestValueProducts.map((product) => (
                <ProductCard key={product.id} product={product} />
              ))}
            </div>
          </div>
        </section>
      )}

      {/* Category Grid */}
      <CategoryGrid />

      {/* Newsletter Signup */}
      <NewsletterSignup />
    </div>
  );
}
