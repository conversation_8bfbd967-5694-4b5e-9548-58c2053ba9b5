// OffGridFlux - Affiliate Disclosure Component
// FTC-compliant affiliate disclosure banner

'use client';

import { useState } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Info, X, ExternalLink } from 'lucide-react';

export function AffiliateDisclosure() {
  const [isVisible, setIsVisible] = useState(true);
  const [isExpanded, setIsExpanded] = useState(false);

  if (!isVisible) return null;

  return (
    <div className="bg-primary-50 border-b border-primary-200 relative">
      <div className="max-w-7xl mx-auto px-4 py-3">
        <div className="flex items-start gap-3">
          {/* Icon */}
          <div className="flex-shrink-0 mt-0.5">
            <Info className="w-5 h-5 text-primary-600" />
          </div>

          {/* Content */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-1">
              <Badge variant="outline" className="text-xs border-primary-300 text-primary-700">
                FTC Disclosure
              </Badge>
            </div>

            <div className="text-sm text-primary-800">
              <p className="font-medium mb-1">
                Affiliate Disclosure: We earn commissions from qualifying purchases.
              </p>
              
              {!isExpanded ? (
                <p className="text-primary-700">
                  As an Amazon Associate and affiliate partner, we earn from qualifying purchases.
                  This doesn&apos;t affect our review process or recommendations.{' '}
                  <button
                    onClick={() => setIsExpanded(true)}
                    className="underline hover:no-underline font-medium"
                  >
                    Read full disclosure
                  </button>
                </p>
              ) : (
                <div className="text-primary-700 space-y-2">
                  <p>
                    OffGridFlux participates in affiliate programs with Amazon, Jackery, Goal Zero, EcoFlow, and other retailers. 
                    When you click on links to products and make a purchase, we may receive a commission at no additional cost to you.
                  </p>
                  <p>
                    <strong>Our Promise:</strong> We only recommend products we&apos;ve personally tested or would use ourselves.
                    Our reviews are based on real-world testing and are not influenced by affiliate relationships. 
                    We purchase most products at full retail price for testing.
                  </p>
                  <p>
                    Commission earnings help support our independent testing lab and keep our content free for readers. 
                    For complete details, see our{' '}
                    <a 
                      href="/affiliate-disclosure" 
                      className="underline hover:no-underline font-medium inline-flex items-center gap-1"
                    >
                      full affiliate disclosure policy
                      <ExternalLink className="w-3 h-3" />
                    </a>
                    .
                  </p>
                  
                  <button 
                    onClick={() => setIsExpanded(false)}
                    className="underline hover:no-underline font-medium text-xs"
                  >
                    Show less
                  </button>
                </div>
              )}
            </div>
          </div>
          
          {/* Close Button */}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsVisible(false)}
            className="flex-shrink-0 h-8 w-8 p-0 text-primary-600 hover:bg-primary-100"
            aria-label="Close disclosure"
          >
            <X className="w-4 h-4" />
          </Button>
        </div>
      </div>
    </div>
  );
}
