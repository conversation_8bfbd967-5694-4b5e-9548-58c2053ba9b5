{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [], "dynamicRoutes": [{"page": "/blog/[year]/[month]/[slug]", "regex": "^/blog/([^/]+?)/([^/]+?)/([^/]+?)(?:/)?$", "routeKeys": {"nxtPyear": "nxtPyear", "nxtPmonth": "nxtPmonth", "nxtPslug": "nxtPslug"}, "namedRegex": "^/blog/(?<nxtPyear>[^/]+?)/(?<nxtPmonth>[^/]+?)/(?<nxtPslug>[^/]+?)(?:/)?$"}, {"page": "/category/[slug]", "regex": "^/category/([^/]+?)(?:/)?$", "routeKeys": {"nxtPslug": "nxtPslug"}, "namedRegex": "^/category/(?<nxtPslug>[^/]+?)(?:/)?$"}, {"page": "/product/[slug]", "regex": "^/product/([^/]+?)(?:/)?$", "routeKeys": {"nxtPslug": "nxtPslug"}, "namedRegex": "^/product/(?<nxtPslug>[^/]+?)(?:/)?$"}], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/about", "regex": "^/about(?:/)?$", "routeKeys": {}, "namedRegex": "^/about(?:/)?$"}, {"page": "/best-portable-power-stations-2025", "regex": "^/best\\-portable\\-power\\-stations\\-2025(?:/)?$", "routeKeys": {}, "namedRegex": "^/best\\-portable\\-power\\-stations\\-2025(?:/)?$"}, {"page": "/favicon.ico", "regex": "^/favicon\\.ico(?:/)?$", "routeKeys": {}, "namedRegex": "^/favicon\\.ico(?:/)?$"}, {"page": "/guides/portable-power-station-buying-guide", "regex": "^/guides/portable\\-power\\-station\\-buying\\-guide(?:/)?$", "routeKeys": {}, "namedRegex": "^/guides/portable\\-power\\-station\\-buying\\-guide(?:/)?$"}, {"page": "/reviews/jackery-explorer-1000-v2-review", "regex": "^/reviews/jackery\\-explorer\\-1000\\-v2\\-review(?:/)?$", "routeKeys": {}, "namedRegex": "^/reviews/jackery\\-explorer\\-1000\\-v2\\-review(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc", "prefetchSegmentHeader": "Next-Router-Segment-Prefetch", "prefetchSegmentSuffix": ".segment.rsc", "prefetchSegmentDirSuffix": ".segments"}, "rewriteHeaders": {"pathHeader": "x-nextjs-rewritten-path", "queryHeader": "x-nextjs-rewritten-query"}, "rewrites": []}