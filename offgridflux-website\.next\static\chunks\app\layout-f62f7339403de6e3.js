(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{347:()=>{},395:(e,t,n)=>{"use strict";n.d(t,{GoogleAnalytics:()=>c,Sz:()=>l,k8:()=>s});var a=n(5155),i=n(2115),r=n(5695),o=n(1335);function c(){let e=(0,r.usePathname)();return((0,i.useEffect)(()=>{(0,o.initGA)();let e=(0,o.initScrollTracking)(),t=(0,o.initTimeTracking)();return()=>{e&&e(),t&&t()}},[]),(0,i.useEffect)(()=>{(0,o.trackPageView)(e)},[e]),o.GA_MEASUREMENT_ID&&"G-XXXXXXXXXX"!==o.GA_MEASUREMENT_ID)?(0,a.jsxs)(a.Frag<PERSON>,{children:[(0,a.jsx)("script",{async:!0,src:"https://www.googletagmanager.com/gtag/js?id=".concat(o.GA_MEASUREMENT_ID)}),(0,a.jsx)("script",{dangerouslySetInnerHTML:{__html:"\n            window.dataLayer = window.dataLayer || [];\n            function gtag(){dataLayer.push(arguments);}\n            gtag('js', new Date());\n            gtag('config', '".concat(o.GA_MEASUREMENT_ID,"', {\n              page_title: document.title,\n              page_location: window.location.href,\n            });\n          ")}})]}):null}function l(){return{trackClick:(e,t,a,i,r)=>{Promise.resolve().then(n.bind(n,1335)).then(n=>{let{trackAffiliateClick:o}=n;o(e,t,a,i,r)})}}}function s(){return{trackView:(e,t,a,i,r)=>{Promise.resolve().then(n.bind(n,1335)).then(n=>{let{trackProductView:o}=n;o(e,t,a,i,r)})}}}},1335:(e,t,n)=>{"use strict";n.r(t),n.d(t,{GA_MEASUREMENT_ID:()=>a,initGA:()=>i,initScrollTracking:()=>S,initTimeTracking:()=>X,setCustomDimensions:()=>y,trackABTest:()=>E,trackAffiliateClick:()=>c,trackContentEngagement:()=>g,trackConversionFunnel:()=>p,trackEmailInteraction:()=>v,trackError:()=>k,trackEvent:()=>o,trackNewsletterSignup:()=>s,trackPageView:()=>r,trackPerformanceMetric:()=>f,trackProductView:()=>l,trackPurchaseIntent:()=>w,trackScrollDepth:()=>_,trackSearch:()=>d,trackSocialShare:()=>h,trackTimeOnPage:()=>m,trackUserPreference:()=>u});let a="G-XXXXXXXXXX",i=()=>{let e=document.createElement("script");e.src="https://www.googletagmanager.com/gtag/js?id=".concat(a),e.async=!0,document.head.appendChild(e),window.dataLayer=window.dataLayer||[],window.gtag=function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];window.dataLayer.push(t)},window.gtag("js",new Date),window.gtag("config",a,{page_title:document.title,page_location:window.location.href})},r=(e,t)=>{window.gtag&&window.gtag("config",a,{page_title:t||document.title,page_location:e})},o=(e,t,n,a,i)=>{window.gtag&&window.gtag("event",e,{event_category:t,event_label:n,value:a,...i})},c=(e,t,n,a,i)=>{o("affiliate_click","ecommerce",e,a,{product_id:t,product_name:e,product_brand:n,product_price:a,affiliate_url:i,currency:"USD"})},l=(e,t,n,a,i)=>{window.gtag&&window.gtag("event","view_item",{currency:"USD",value:a,items:[{item_id:t,item_name:e,item_brand:n,item_category:i,price:a,quantity:1}]})},s=e=>{o("newsletter_signup","engagement",e,void 0,{signup_source:e})},d=(e,t)=>{o("search","engagement",e,t,{search_term:e,results_count:t})},g=(e,t,n,a)=>{o(n,"content",t,a,{content_type:e,content_id:t})},_=(e,t)=>{o("scroll_depth","engagement",t,e,{scroll_percentage:e,page_path:t})},m=(e,t)=>{o("time_on_page","engagement",t,e,{time_seconds:e,page_path:t})},w=(e,t,n,a,i)=>{window.gtag&&window.gtag("event","view_review"===i?"view_item":"click_affiliate"===i?"select_item":"add_to_cart",{currency:"USD",value:a,items:[{item_id:t,item_name:e,item_brand:n,item_category:"Portable Power Station",price:a,quantity:1}]})},u=(e,t)=>{o("user_preference","personalization",e,void 0,{preference_type:e,preference_value:t})},p=(e,t,n)=>{o("conversion_funnel","ecommerce",e,void 0,{funnel_stage:e,traffic_source:t,product_id:n})},h=(e,t,n)=>{o("social_share","engagement",e,void 0,{social_platform:e,content_type:t,content_id:n})},v=(e,t,n)=>{o("email_interaction","engagement",e,void 0,{email_action:e,campaign_id:t,email_type:n})},f=(e,t,n)=>{o("performance_metric","technical",e,t,{metric_name:e,metric_value:t,page_path:n})},k=function(e,t,n){let a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"medium";o("error","technical",e,void 0,{error_type:e,error_message:t,error_severity:a,page_path:n})},E=(e,t,n)=>{o("ab_test","optimization",e,void 0,{test_name:e,test_variant:t,test_action:n})},y=e=>{window.gtag&&window.gtag("config",a,{custom_map:e})},S=()=>{let e=0,t=[25,50,75,90,100],n=new Set,a=()=>{let a=Math.round(window.scrollY/(document.documentElement.scrollHeight-window.innerHeight)*100);a>e&&(e=a,t.forEach(e=>{a>=e&&!n.has(e)&&(n.add(e),_(e,window.location.pathname))}))};return window.addEventListener("scroll",a,{passive:!0}),()=>{window.removeEventListener("scroll",a)}},X=()=>{let e=Date.now(),t=()=>{m(Math.round((Date.now()-e)/1e3),window.location.pathname)};return document.addEventListener("visibilitychange",()=>{"hidden"===document.visibilityState&&t()}),window.addEventListener("beforeunload",t),t}},1666:e=>{e.exports={style:{fontFamily:"'Inter', 'Inter Fallback'",fontStyle:"normal"},className:"__className_e8ce0c",variable:"__variable_e8ce0c"}},4847:(e,t,n)=>{Promise.resolve().then(n.t.bind(n,1666,23)),Promise.resolve().then(n.t.bind(n,347,23)),Promise.resolve().then(n.bind(n,395))},5695:(e,t,n)=>{"use strict";var a=n(8999);n.o(a,"usePathname")&&n.d(t,{usePathname:function(){return a.usePathname}})}},e=>{var t=t=>e(e.s=t);e.O(0,[258,441,684,358],()=>t(4847)),_N_E=e.O()}]);