// OffGridFlux - Hero Component
// Modern hero section for the homepage

import { <PERSON><PERSON> } from '@/components/ui/button';
import { <PERSON>ap, Shield, Users, Star, ArrowRight, Clock, CheckCircle } from 'lucide-react';

export function Hero() {
  return (
    <section className="relative min-h-screen bg-white overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0">
        {/* Gradient Orbs */}
        <div className="absolute top-20 left-10 w-72 h-72 bg-primary-500/20 rounded-full blur-3xl"></div>
        <div className="absolute top-40 right-20 w-96 h-96 bg-energy-500/15 rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 left-1/3 w-80 h-80 bg-warning-500/10 rounded-full blur-3xl"></div>

        {/* Grid Pattern */}
        <div className="absolute inset-0 bg-[linear-gradient(to_right,#f1f5f9_1px,transparent_1px),linear-gradient(to_bottom,#f1f5f9_1px,transparent_1px)] bg-[size:4rem_4rem] opacity-30"></div>
      </div>

      {/* Main Content */}
      <div className="relative z-10 container mx-auto px-4 pt-20 pb-20">
        <div className="max-w-7xl mx-auto">
          {/* Header Section */}
          <div className="text-center mb-16">
            {/* Trust Badges */}
            <div className="flex flex-wrap justify-center gap-3 mb-8">
              <div className="flex items-center gap-2 bg-green-50 text-green-700 rounded-full px-4 py-2 border border-green-200">
                <CheckCircle className="w-4 h-4" />
                <span className="text-sm font-medium">FTC Compliant</span>
              </div>
              <div className="flex items-center gap-2 bg-yellow-50 text-yellow-700 rounded-full px-4 py-2 border border-yellow-200">
                <Star className="w-4 h-4" />
                <span className="text-sm font-medium">Expert Tested</span>
              </div>
              <div className="flex items-center gap-2 bg-blue-50 text-blue-700 rounded-full px-4 py-2 border border-blue-200">
                <Users className="w-4 h-4" />
                <span className="text-sm font-medium">Real Reviews</span>
              </div>
            </div>

            {/* Main Headline */}
            <h1 className="text-5xl md:text-6xl lg:text-7xl font-bold text-neutral-900 leading-tight mb-6">
              Unbiased Power Station
              <br />
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-primary-600 to-energy-600">Reviews & Testing</span>
            </h1>

            <p className="text-xl md:text-2xl text-neutral-600 max-w-3xl mx-auto mb-12 leading-relaxed">
              Real-world testing and honest reviews to help you choose the perfect portable power solution for your off-grid adventures.
            </p>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-16">
              <Button size="lg" className="bg-primary-600 hover:bg-primary-700 text-white px-8 py-4 text-lg font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300">
                View Top Rated Power Stations
                <ArrowRight className="ml-2 w-5 h-5" />
              </Button>
              <Button variant="outline" size="lg" className="border-neutral-300 text-neutral-700 hover:bg-neutral-50 px-8 py-4 text-lg font-semibold rounded-xl">
                Browse All Reviews
              </Button>
            </div>
          </div>

          {/* Stats Grid */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 mb-16">
            <div className="text-center p-6 bg-white rounded-2xl shadow-lg border border-neutral-200 hover:shadow-xl transition-shadow duration-300">
              <div className="w-12 h-12 bg-primary-100 rounded-xl flex items-center justify-center mx-auto mb-4">
                <Zap className="w-6 h-6 text-primary-600" />
              </div>
              <div className="text-3xl font-bold text-neutral-900 mb-2">17+</div>
              <div className="text-neutral-600 font-medium">Products Tested</div>
              <div className="text-neutral-500 text-sm mt-1">Comprehensive testing</div>
            </div>

            <div className="text-center p-6 bg-white rounded-2xl shadow-lg border border-neutral-200 hover:shadow-xl transition-shadow duration-300">
              <div className="w-12 h-12 bg-energy-100 rounded-xl flex items-center justify-center mx-auto mb-4">
                <Clock className="w-6 h-6 text-energy-600" />
              </div>
              <div className="text-3xl font-bold text-neutral-900 mb-2">30+</div>
              <div className="text-neutral-600 font-medium">Days Per Test</div>
              <div className="text-neutral-500 text-sm mt-1">Thorough evaluation</div>
            </div>

            <div className="text-center p-6 bg-white rounded-2xl shadow-lg border border-neutral-200 hover:shadow-xl transition-shadow duration-300">
              <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center mx-auto mb-4">
                <Shield className="w-6 h-6 text-green-600" />
              </div>
              <div className="text-3xl font-bold text-neutral-900 mb-2">100%</div>
              <div className="text-neutral-600 font-medium">Independent</div>
              <div className="text-neutral-500 text-sm mt-1">No manufacturer bias</div>
            </div>

            <div className="text-center p-6 bg-white rounded-2xl shadow-lg border border-neutral-200 hover:shadow-xl transition-shadow duration-300">
              <div className="w-12 h-12 bg-warning-100 rounded-xl flex items-center justify-center mx-auto mb-4">
                <Users className="w-6 h-6 text-warning-600" />
              </div>
              <div className="text-3xl font-bold text-neutral-900 mb-2">10K+</div>
              <div className="text-neutral-600 font-medium">Monthly Readers</div>
              <div className="text-neutral-500 text-sm mt-1">Trusted community</div>
            </div>
          </div>

          {/* Featured Review Card */}
          <div className="max-w-4xl mx-auto">
            <div className="bg-gradient-to-r from-primary-50 to-energy-50 rounded-3xl p-8 md:p-12 border border-primary-200 shadow-xl">
              <div className="text-center mb-8">
                <div className="flex justify-center mb-4">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="w-6 h-6 text-yellow-400 fill-current" />
                  ))}
                </div>
                <blockquote className="text-xl md:text-2xl text-neutral-700 italic font-light leading-relaxed">
                  &ldquo;OffGridFlux provides the most thorough and unbiased portable power station reviews. Their real-world testing helped me choose the perfect setup for my off-grid cabin.&rdquo;
                </blockquote>
                <div className="mt-6 flex items-center justify-center gap-4">
                  <div className="w-12 h-12 bg-primary-200 rounded-full flex items-center justify-center">
                    <span className="text-primary-700 font-bold">SM</span>
                  </div>
                  <div className="text-left">
                    <div className="font-semibold text-neutral-900">Sarah M.</div>
                    <div className="text-neutral-600 text-sm">Verified Reader</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
